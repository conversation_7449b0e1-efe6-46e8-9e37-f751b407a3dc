package com.workplat.accept.business.chat.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class BizChatMessageVO {


    @Schema(description = "会话ID")
    private String conversationId;

    @Schema(description = "发送者")
    private String sender;

    @Schema(description = "消息内容")
    private String content;

    @Schema(description = "创建时间")
    private Date createTime;
}
