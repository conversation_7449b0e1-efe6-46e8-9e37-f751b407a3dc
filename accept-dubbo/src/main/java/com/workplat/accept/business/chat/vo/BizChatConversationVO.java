package com.workplat.accept.business.chat.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class BizChatConversationVO {

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "会话ID")
    private String id;

    @Schema(description = "会话标题")
    private String title;

    @Schema(description = "创建时间")
    private Date  createTime;

    @Schema(description = "更新时间")
    private Date  updateTime;
}
