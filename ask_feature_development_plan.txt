**开发计划：在 `ask` 方法中实现对话存储 (已根据DTO/VO更新)**

1.  **修改 `ChatApiImpl.java`**
    *   **注入依赖**:
        *   注入 `BizChatConversationService` 用于会话管理。
        *   注入 `BizChatMessageService` 用于消息管理。
    *   **修改 `ask` 方法逻辑**:
        *   **确定/获取会话ID (finalChatId)**:
            *   检查 `ask.getChatId()`。
            *   **如果 `ask.getChatId()` 为空 (新会话)**:
                *   这部分会比较棘手，因为Dify的 `conversation_id` (`streamMessageVO.getConversationId()`) 会在流式响应中返回。我们需要一种机制来捕获第一个返回的 `conversation_id` 并用它创建 `BizChatConversation`。这可能需要在 `Flux` 处理中进行，或者通过一个 `AtomicReference` 或类似的机制在外部捕获。
                *   `BizChatConversation` 将使用从 `StreamMessageVO.getConversationId()` 获取的ID作为其主键，并关联 `ask.getUserId()`。
            *   **如果 `ask.getChatId()` 已存在**:
                *   直接使用 `ask.getChatId()` 作为 `finalChatId`。
        *   **存储用户提问**:
            *   在调用Dify服务 *之前*，如果 `ask.getChatId()` 已存在，则可以直接存储。
            *   如果 `ask.getChatId()` 为空，用户消息的存储需要等到从Dify流中成功获取并创建了 `conversation_id` 之后。
            *   创建一个 `BizChatMessage` 对象：
                *   `conversationId`: `finalChatId`。
                *   `content`: `ask.getQuestions()`。
                *   `senderType`: "USER" (或定义一个枚举/常量，如 `MessageSenderType.USER`)。
                *   `userId`: `ask.getUserId()`。
            *   调用 `bizChatMessageService.createMessage()` 保存用户消息。
        *   **处理Dify流式响应并存储AI回复**:
            *   在 `difyServiceHelper.streamingMessage(ask, ask.getAppKey()).flatMap(...)` 或 `doOnNext(...)` 等操作中处理。
            *   **会话创建 (仅限新会话时)**:
                *   对于新会话，从第一个 `StreamMessageVO` 中获取 `streamMessageVO.getConversationId()`。
                *   如果这是流中的第一个有效 `conversation_id` 且 `ask.getChatId()` 为空：
                    *   `finalChatId = streamMessageVO.getConversationId()`
                    *   创建 `BizChatConversation` 实例: `id = finalChatId`, `userId = ask.getUserId()`, `title` (可选, e.g., `ask.getQuestions().substring(0, Math.min(ask.getQuestions().length(), 50))`).
                    *   调用 `bizChatConversationService.createConversation()`。
                    *   此时，之前未能存储的用户提问消息现在可以存储了（使用此 `finalChatId`）。
            *   **AI消息聚合与存储**:
                *   Dify通过 `event: "llm_chunk"` 和 `answer` 字段流式传输AI回复。我们需要累积这些 `answer` 片段。
                *   当收到 `event: "message_end"` 事件时，表示AI回复结束。此时，将累积的AI回复内容进行存储。
                *   创建一个 `BizChatMessage` 对象：
                    *   `conversationId`: 当前会话的 `finalChatId`。
                    *   `content`: 累积的AI回复 (来自多个 `streamMessageVO.getAnswer()` 的拼接)。
                    *   `senderType`: "ASSISTANT" (或 `MessageSenderType.ASSISTANT`)。
                    *   `userId`: null (或一个代表AI的系统用户ID，如果需要)。
                *   调用 `bizChatMessageService.createMessage()` 保存AI消息。
            *   确保在 `map` 操作中正确处理 `ServerSentEvent` 的构建，同时执行上述数据库操作。数据库操作是副作用，需要小心处理在响应式流中的位置，可能使用 `doOnNext`, `doOnComplete`, `flatMap` 等。

2.  **`AskDTO` 和 `StreamMessageVO` 结构确认 (已完成)**
    *   `AskDTO`:
        *   用户ID: `ask.getUserId()`
        *   传入的会话ID: `ask.getChatId()`
    *   `StreamMessageVO`:
        *   Dify生成的会话ID: `streamMessageVO.getConversationId()` (用作 `BizChatConversation` 的主键/ID)
        *   AI回复内容块: `streamMessageVO.getAnswer()`
        *   AI回复事件: 主要关注 `event: "llm_chunk"` 用于内容聚合，`event: "message_end"` 用于触发存储。`event: "agent_thought"` 或其他事件可能也包含 `conversation_id`，可用于尽早获取。

3.  **`BizChatMessage` 和 `BizChatConversation` 实体设计 (与原计划一致)**
    *   `BizChatMessage`: `id` (PK), `conversationId` (String, 对应Dify的ID), `userId` (String), `senderType` (String/Enum: "USER", "ASSISTANT"), `content` (String), `timestamp` (LocalDateTime/Date).
    *   `BizChatConversation`: `id` (String, PK, **使用Dify返回的 `conversation_id`**), `userId` (String), `title` (String, 可选), `createTime`, `updateTime`.

4.  **错误处理和事务管理 (与原计划一致)**
    *   对数据库操作添加错误处理。
    *   对于新会话，创建会话记录、保存用户消息、保存第一条AI消息可以考虑在一个逻辑单元内完成，但由于流式特性，传统事务可能难以直接应用。需要保证最终一致性，例如，如果会话创建成功但消息保存失败，应有补偿或重试机制，或至少记录错误。 