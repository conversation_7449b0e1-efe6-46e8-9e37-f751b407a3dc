package com.workplat.matter.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.vo
 * @description
 * @date 2025/6/5 13:24
 */
@Data
@Schema(description = "事项扩展信息DTO")
public class ConfMatterExtendDTO {

    @Schema(description = "事项id")
    private String matterId;

    @Schema(description = "事项编码")
    private String matterCode;

    @Schema(description = "流程编码")
    private String flowCode;

    @Schema(description = "信息提交后的告知内容")
    private String informAfterSubmit;

    @Schema(description = "关联服务id，多个id用逗号拼接", example = "0ce1ffdffe4b497dafb0bba4f874a94e,7c88ac420e8148c0944cec78c6f56856")
    private String serveIds;

    @Schema(description = "知情同意内容", example = "{\"title\":\"知情同意标题\",\"content\":\"内容xxxxxxxxxxxxxxxx\"}")
    private String informedConsent;
}
