package com.workplat.matter.api;

import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import com.workplat.matter.vo.ConfMatterExtendDTO;
import com.workplat.matter.vo.ConfMatterExtendVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.api
 * @description 事项信息Api
 * @date 2025/5/21 16:11
 */
@Validated
@RestController
@RequestMapping("/api/matter/blbb/")
@Tag(name = "边聊边办事项信息模块")
public interface MatterInfoApi {

    @GetMapping("/getByName")
    @Operation(summary = "通过名称获取事项信息")
    @ApiLogging(module = "边聊边办事项信息模块", operation = "通过名称获取事项信息", type = OperationType.QUERY)
    ResponseData<ConfMatterExtendVO> getByName(@NotBlank(message = "名称不能为空") String name);

    @GetMapping("/getByMatterId")
    @Operation(summary = "通过事项id获取扩展信息")
    @ApiLogging(module = "边聊边办事项信息模块", operation = "通过事项id获取扩展信息", type = OperationType.QUERY)
    ResponseData<ConfMatterExtendVO> getByMatterId(String matterId);

    @GetMapping("/getByMatterCode")
    @Operation(summary = "通过事项code获取扩展信息")
    @ApiLogging(module = "边聊边办事项信息模块", operation = "通过事项code获取扩展信息", type = OperationType.QUERY)
    ResponseData<ConfMatterExtendVO> getByMatterCode(String matterCode);

    @PostMapping("/saveExtend")
    @Operation(summary = "保存事项扩展信息")
    @ApiLogging(module = "边聊边办事项信息模块", operation = "保存事项扩展信息", type = OperationType.INSERT)
    ResponseData<Void> saveExtend(@RequestBody ConfMatterExtendDTO dto);
}
