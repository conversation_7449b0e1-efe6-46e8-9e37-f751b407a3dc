package com.workplat.matter.api;

import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import com.workplat.matter.vo.AuthOperationDTO;
import com.workplat.matter.vo.AuthOperationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.api
 * @description
 * @date 2025/6/6 13:33
 */
@Validated
@RestController
@RequestMapping("/api/matter/auth/")
@Tag(name = "授权操作模块")
public interface AuthOperationApi {

    @PostMapping("/createAuth")
    @Operation(summary = "生成授权请求")
    @ApiLogging(module = "授权操作模块", operation = "生成授权请求", type = OperationType.INSERT)
    ResponseData<Void> createAuth(@Valid @RequestBody AuthOperationDTO dto);

    @PostMapping("/checkAuth")
    @Operation(summary = "查看授权请求")
    @ApiLogging(module = "授权操作模块", operation = "查看授权请求", type = OperationType.QUERY)
    ResponseData<AuthOperationVO> checkAuth(@Valid @RequestBody AuthOperationDTO dto);

    @PostMapping("/updateAuth")
    @Operation(summary = "更新授权操作")
    @ApiLogging(module = "授权操作模块", operation = "更新授权操作", type = OperationType.UPDATE)
    ResponseData<Void> updateAuth(@Valid @RequestBody AuthOperationDTO dto);

    @GetMapping("/isRead")
    @Operation(summary = "是否已读")
    @ApiLogging(module = "授权操作模块", operation = "是否已读", type = OperationType.QUERY)
    ResponseData<Boolean> isRead(@NotBlank(message = "userId不能为空")String userId, @NotBlank(message = "readCode不能为空")String readCode);

    @GetMapping("/setRead")
    @Operation(summary = "记录已读")
    @ApiLogging(module = "授权操作模块", operation = "记录已读", type = OperationType.INSERT)
    ResponseData<Void> setRead(@NotBlank(message = "userId不能为空")String userId, @NotBlank(message = "readCode不能为空")String readCode);
}
