
package com.workplat.gss.application.api.api;

import com.workplat.gss.application.api.dto.BizInstanceQuotaDto;
import com.workplat.gss.application.dubbo.vo.BizInstanceQuotaVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceSituationVO;
import com.workplat.gss.application.dubbo.vo.ConfMatterAcceptQuotaVO;
import com.workplat.gss.common.core.response.ResponseData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Validated
@RestController
@Tag(name = "申报情形模块")
@RequestMapping(value = "/api/biz/instance/situation")
public interface BizInstanceSituationApi {

    @PostMapping("/submit")
    @Operation(summary = "生成一次告知")
    ResponseData<List<BizInstanceQuotaVO>> save(@Validated @RequestBody List<BizInstanceQuotaDto> dto);

    @GetMapping("/instanceId")
    @Operation(summary = "查询一次告知")
    ResponseData<BizInstanceSituationVO> query(String instanceId);

    @GetMapping("/quota")
    @Operation(summary = "查询办理情形")
    ResponseData<List<ConfMatterAcceptQuotaVO>> get(String instanceId);
}
