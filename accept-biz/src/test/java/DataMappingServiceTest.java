import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.mapping.ConfFieldMapping;
import com.workplat.accept.business.mapping.DataMappingService;
import com.workplat.accept.business.mapping.FieldMappingConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.HashMap;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
public class DataMappingServiceTest {

    @Mock
    private FieldMappingConfig fieldMappingConfig;

    @InjectMocks
    private DataMappingService dataMappingService;

    private Map<String, String> thirdPartyToTemplateMap;
    private Map<String, String> templateToFormMap;

    @BeforeEach
    void setUp() {
        // 配置示例 - 处理各种数据结构 mappings{ 第三方编码：模板编码}
//        Map<String, String> thirdToTemplate = new HashMap<>();
//
//// 1. 直接对象
////        thirdToTemplate.put("$.fwbh", "contractNo");
////        thirdToTemplate.put("$.xmmc", "projectName");
//
//// 2. data数组
//        thirdToTemplate.put("$.data[*].fwbh", "contractNo");
//        thirdToTemplate.put("$.data[*].xmmc", "projectName");
//
//// 3. data对象
////        thirdToTemplate.put("$.data.fwbh", "contractNo");
////        thirdToTemplate.put("$.data.xmmc", "projectName");
//
//// 4. 深层嵌套
////        thirdToTemplate.put("$.result.data.fwbh", "contractNo");
////        thirdToTemplate.put("$.result.data.xmmc", "projectName");
//
// 表单映射 示例mappings{ 模板编码：表单编码}
        Map<String, String> templateToForm = new HashMap<>();
        templateToForm.put("contractNo", "no");
        templateToForm.put("projectName", "name");


        // 1. 处理数组数据的配置 { 第三方编码：模板编码}
        Map<String, String> arrayConfig = new HashMap<>();
        arrayConfig.put("$.data[*].fwbh", "contractNo");
        arrayConfig.put("$.data[*].xmmc", "projectName");
        arrayConfig.put("$.data[*].fwzmj", "area");

// 2. 处理对象数据的配置
        Map<String, String> objectConfig = new HashMap<>();
        objectConfig.put("$.data.fwbh", "contractNo");
        objectConfig.put("$.data.xmmc", "projectName");
        objectConfig.put("$.data.fwzmj", "area");

// 3. 处理直接对象的配置
        Map<String, String> directConfig = new HashMap<>();
        directConfig.put("$.fwbh", "contractNo");
        directConfig.put("$.xmmc", "projectName");
        directConfig.put("$.fwzmj", "area");

// 4. 处理嵌套对象的配置
        Map<String, String> nestedConfig = new HashMap<>();
        nestedConfig.put("$.result.data.fwbh", "contractNo");
        nestedConfig.put("$.result.data.xmmc", "projectName");
        nestedConfig.put("$.result.data.fwzmj", "area");

// 保存配置
        ConfFieldMapping config = ConfFieldMapping.builder()
                .thirdToTemplate(JSON.toJSONString(arrayConfig))
                .templateToForm(JSON.toJSONString(templateToForm))
                .build();

        when(fieldMappingConfig.getThirdToTemplate())
                .thenReturn(config.getThirdToTemplate());
        when(fieldMappingConfig.getTemplateToForm())
                .thenReturn(config.getTemplateToForm());

    }


    public static void main(String[] args) {
        String string = "{\"tableName\":\"\",\"renderList\":[{\"icon\":\"icon-biaodan\",\"name\":\"表单域\",\"platform\":\"all\",\"type\":\"formArea\",\"needSpecial\":false,\"componentName\":\"ANetFormArea\",\"props\":{\"bgColor\":\"#dd4b39\",\"title\":\"新不动产权共有方式\",\"titleSize\":22,\"titleColor\":\"#0E0D0D\",\"barColor\":\"\",\"isHidden\":false,\"functions\":[],\"isShowButton\":true,\"arrowColor\":\"#000000\",\"show\":true,\"field\":\"x5omNn2Sb\"},\"events\":{},\"child\":[{\"icon\":\"icon-icon-\",\"name\":\"栅格\",\"platform\":\"pc\",\"type\":\"grid\",\"needSpecial\":false,\"props\":{\"columns\":[{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-select\",\"name\":\"下拉框\",\"platform\":\"all\",\"componentName\":\"a-net-select\",\"needSpecial\":false,\"needRules\":true,\"props\":{\"label\":\"新不动产权证共有方式\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"tableWidth\":\"\",\"field\":\"xbdcqzgyfs\",\"disabled\":false,\"default\":\"\",\"modelValue\":\"\",\"optionKey\":\"\",\"key\":\"xbdcqzgyfs\",\"placeholder\":\"请选择\",\"functions\":[{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changexbdcqzgyfs\"}],\"options\":[{\"value\":\"共同共有\",\"label\":\"共同共有\",\"disabled\":false},{\"value\":\"按份共有\",\"label\":\"按份共有\",\"disabled\":false},{\"label\":\"单独所有\",\"value\":\"单独所有\",\"disabled\":false}],\"id\":\"c4b0e98c4ab947998d3974ea2476b0fd\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"新不动产权证共有方式不能为空\",\"trigger\":\"blur\"}],\"id\":\"8D2Es_iX6\"}],\"id\":\"OPOWvuMYMo\"},{\"span\":12,\"child\":[{\"icon\":\"icon-select\",\"name\":\"下拉框\",\"platform\":\"all\",\"componentName\":\"a-net-select\",\"needSpecial\":false,\"needRules\":true,\"props\":{\"label\":\"份额格式\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"tableWidth\":\"\",\"field\":\"fegs\",\"disabled\":false,\"default\":\"\",\"modelValue\":\"\",\"optionKey\":\"\",\"key\":\"fegs\",\"placeholder\":\"请选择\",\"functions\":[{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changefegs\"}],\"options\":[{\"value\":\"百分数\",\"label\":\"百分数\",\"disabled\":false},{\"value\":\"分数\",\"label\":\"分数\",\"disabled\":false}],\"id\":\"67cf35c8387e48ab8569fa920c757cbf\"},\"events\":{},\"child\":[],\"rules\":[],\"id\":\"ijB6vcTu-\"}],\"offset\":0,\"push\":0,\"pull\":0,\"id\":\"hmI6VeHdFh\"}],\"gutter\":40,\"justify\":\"start\",\"align\":\"top\",\"functions\":[],\"field\":\"e7XlWNTo8\"},\"events\":{},\"child\":[],\"rules\":[],\"id\":\"YDYSuBEqfX\"}],\"rules\":[],\"id\":\"GiaAItPh4W\"},{\"icon\":\"icon-biaodan\",\"name\":\"表单域\",\"platform\":\"all\",\"type\":\"formArea\",\"needSpecial\":false,\"componentName\":\"ANetFormArea\",\"props\":{\"bgColor\":\"#dd4b39\",\"title\":\"标题\",\"titleSize\":22,\"titleColor\":\"#0E0D0D\",\"barColor\":\"\",\"isHidden\":false,\"functions\":[],\"isShowButton\":true,\"arrowColor\":\"#000000\",\"show\":true,\"field\":\"5mTzI6wKF\"},\"events\":{},\"child\":[{\"icon\":\"icon-biaodan\",\"name\":\"可新增表格\",\"platform\":\"all\",\"type\":\"addTable\",\"needSpecial\":false,\"componentName\":\"ANetCanAddTable\",\"props\":{\"functions\":[{\"name\":\"delete事件\",\"value\":\"\",\"key\":\"deleteNwIvgyRx0\"},{\"name\":\"add事件\",\"value\":\"\",\"key\":\"addNwIvgyRx0\"}],\"title\":\"被继承人信息\",\"deleteText\":\"删除\",\"addText\":\"新增\",\"defaultLine\":0,\"isShowAsTable\":false,\"isShowIndex\":true,\"isSelection\":false,\"isAddByDialog\":false,\"isShowOutBorder\":true,\"isShowInnerBorder\":true,\"innerBorderColor\":\"#000\",\"outerBorderColor\":\"#000\",\"innerWidth\":1,\"outerWidth\":1,\"key\":\"\",\"isNeedMax\":false,\"isNeedMin\":false,\"showColumns\":\"\",\"min\":1,\"max\":1,\"field\":\"bjcrxxTable\"},\"events\":{},\"child\":[{\"child\":[{\"icon\":\"icon-icon-\",\"name\":\"栅格\",\"platform\":\"pc\",\"type\":\"grid\",\"needSpecial\":false,\"props\":{\"columns\":[{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-input\",\"name\":\"输入框\",\"componentName\":\"a-net-input\",\"needSpecial\":false,\"platform\":\"all\",\"needRules\":true,\"props\":{\"label\":\"被继承人姓名\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"field\":\"bjcrxm\",\"key\":\"bjcrxm\",\"modelValue\":\"\",\"placeholder\":\"请在此处输入内容\",\"tableWidth\":\"\",\"clearable\":false,\"disabled\":false,\"readonly\":false,\"maxlength\":50,\"default\":\"\",\"isShowLimit\":false,\"showAppendBtn\":false,\"buttonText\":\"\",\"appendBtnColor\":\"#2c8ef1\",\"functions\":[{\"name\":\"input事件\",\"value\":\"console.log(form)\",\"key\":\"inputbjcrxm\"},{\"name\":\"foucs事件\",\"value\":\"\",\"key\":\"foucsbjcrxm\"},{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changebjcrxm\"},{\"name\":\"click事件\",\"value\":\"\",\"key\":\"clickbjcrxm\"}],\"id\":\"4fd205b6448d4f48af73f67262ac6be8\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"被继承人姓名不能为空\",\"trigger\":\"blur\"}],\"id\":\"Ga5rDjH0I\"}],\"id\":\"XCh9ZrIlph\"},{\"span\":12,\"child\":[{\"icon\":\"icon-input\",\"name\":\"输入框\",\"componentName\":\"a-net-input\",\"needSpecial\":false,\"platform\":\"all\",\"needRules\":true,\"props\":{\"label\":\"被继承人身份证号码\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"field\":\"bjcrsfzhm\",\"key\":\"bjcrsfzhm\",\"modelValue\":\"\",\"placeholder\":\"请在此处输入内容\",\"tableWidth\":\"\",\"clearable\":false,\"disabled\":false,\"readonly\":false,\"maxlength\":50,\"default\":\"\",\"isShowLimit\":false,\"showAppendBtn\":false,\"buttonText\":\"\",\"appendBtnColor\":\"#2c8ef1\",\"functions\":[{\"name\":\"input事件\",\"value\":\"console.log(form)\",\"key\":\"inputbjcrsfzhm\"},{\"name\":\"foucs事件\",\"value\":\"\",\"key\":\"foucsbjcrsfzhm\"},{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changebjcrsfzhm\"},{\"name\":\"click事件\",\"value\":\"\",\"key\":\"clickbjcrsfzhm\"}],\"id\":\"1f49ea0faf194cc1a04f84e3c2ff23cd\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"被继承人身份证号码不能为空\",\"trigger\":\"blur\"},{\"validatorStr\":\"\",\"trigger\":\"blur\"},{\"pattern\":\"^[1-9]\\\\d{7}((0\\\\d)|(1[0-2]))(([0|1|2]\\\\d)|3[0-1])\\\\d{3}$|^[1-9]\\\\d{5}[1-9]\\\\d{3}((0\\\\d)|(1[0-2]))(([0|1|2]\\\\d)|3[0-1])((\\\\d{4})|\\\\d{3}[xX])$\",\"message\":\"请输入正确的身份证号码\",\"trigger\":\"blur\"}],\"id\":\"7QMK5nXin\"}],\"offset\":0,\"push\":0,\"pull\":0,\"id\":\"e2m_hkdv38\"},{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-input\",\"name\":\"输入框\",\"componentName\":\"a-net-input\",\"needSpecial\":false,\"platform\":\"all\",\"needRules\":true,\"props\":{\"label\":\"被继承人当前生命状态\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"field\":\"bjcrdqsmzt\",\"key\":\"bjcrdqsmzt\",\"modelValue\":\"死亡\",\"placeholder\":\"请在此处输入内容\",\"tableWidth\":\"\",\"clearable\":false,\"disabled\":false,\"readonly\":false,\"maxlength\":50,\"default\":\"\",\"isShowLimit\":false,\"showAppendBtn\":false,\"buttonText\":\"\",\"appendBtnColor\":\"#2c8ef1\",\"functions\":[{\"name\":\"input事件\",\"value\":\"console.log(form)\",\"key\":\"inputbjcrdqsmzt\"},{\"name\":\"foucs事件\",\"value\":\"\",\"key\":\"foucsbjcrdqsmzt\"},{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changebjcrdqsmzt\"},{\"name\":\"click事件\",\"value\":\"\",\"key\":\"clickbjcrdqsmzt\"}],\"id\":\"d5bfffa0ec2f48edb6c957f621ed462f\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"被继承人当前生命状态不能为空\",\"trigger\":\"blur\"}],\"id\":\"V4btkFWhB\"}],\"id\":\"-M8zRHPN3\"},{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-input\",\"name\":\"输入框\",\"componentName\":\"a-net-input\",\"needSpecial\":false,\"platform\":\"all\",\"needRules\":true,\"props\":{\"label\":\"被继承人所有份额\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"field\":\"bjcrsyfe\",\"key\":\"bjcrsyfe\",\"modelValue\":\"\",\"placeholder\":\"请在此处输入内容\",\"tableWidth\":\"\",\"clearable\":false,\"disabled\":false,\"readonly\":false,\"maxlength\":50,\"default\":\"\",\"isShowLimit\":false,\"showAppendBtn\":false,\"buttonText\":\"\",\"appendBtnColor\":\"#2c8ef1\",\"functions\":[{\"name\":\"input事件\",\"value\":\"console.log(form)\",\"key\":\"inputbjcrsyfe\"},{\"name\":\"foucs事件\",\"value\":\"\",\"key\":\"foucsbjcrsyfe\"},{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changebjcrsyfe\"},{\"name\":\"click事件\",\"value\":\"\",\"key\":\"clickbjcrsyfe\"}],\"id\":\"bc1831e9a1434439b5b433b9aab25212\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"被继承人所有份额不能为空\",\"trigger\":\"blur\"},{\"validatorStr\":\"let now = '';\\r\\nlet type = '百分号'; // 初始假设为百分号类型\\r\\nconsole.log(`初始输入: ${value}, 百分号索引: ${value.indexOf('%')}, 分数索引: ${value.indexOf('/')}`);\\r\\n\\r\\n// 判断输入格式并处理\\r\\nif (value.indexOf('%') > -1) {\\r\\n    now = Number(value.split('%')[0]); // 提取百分号的数字部分\\r\\n    console.log(`输入是百分号，提取的数值: ${now}`);\\r\\n} else if (value.indexOf('/') > -1) {\\r\\n    now = value; // 分数格式，直接保存为字符串\\r\\n    type = '分数'; // 设置类型为分数\\r\\n    console.log(`输入是分数，保存的分数: ${now}`);\\r\\n} else {\\r\\n    console.log('错误：请填写符合格式的内容');\\r\\n    callback(new Error('请填写符合格式的内容')); // 处理不合法的输入\\r\\n    return; // 停止执行后续代码\\r\\n}\\r\\n\\r\\n// 计算最大公约数（GCD）\\r\\nfunction gcd(a, b) {\\r\\n    console.log(`计算GCD: a = ${a}, b = ${b}`);\\r\\n    return b === 0 ? a : gcd(b, a % b);\\r\\n}\\r\\n\\r\\n// 计算最小公倍数（LCM）\\r\\nfunction lcm(a, b) {\\r\\n    const result = (a * b) / gcd(a, b);\\r\\n    console.log(`计算LCM: a = ${a}, b = ${b}, LCM = ${result}`);\\r\\n    return result;\\r\\n}\\r\\n\\r\\n// 字符串转分数\\r\\nfunction parseFraction(str) {\\r\\n    const [numerator, denominator] = str.split('/').map(Number);\\r\\n    console.log(`解析分数: ${str} => { numerator: ${numerator}, denominator: ${denominator} }`);\\r\\n    return { numerator, denominator };\\r\\n}\\r\\n\\r\\n// 分数相加\\r\\nfunction addFractions(f1, f2) {\\r\\n    const commonDenominator = lcm(f1.denominator, f2.denominator);\\r\\n    const numerator1 = f1.numerator * (commonDenominator / f1.denominator);\\r\\n    const numerator2 = f2.numerator * (commonDenominator / f2.denominator);\\r\\n    const resultNumerator = numerator1 + numerator2;\\r\\n\\r\\n    // 简化结果\\r\\n    const commonDivisor = gcd(resultNumerator, commonDenominator);\\r\\n    console.log(`相加结果: { numerator: ${resultNumerator}, denominator: ${commonDenominator} }`);\\r\\n    return {\\r\\n        numerator: resultNumerator / commonDivisor,\\r\\n        denominator: commonDenominator / commonDivisor\\r\\n    };\\r\\n}\\r\\n\\r\\n// 校验继承份额\\r\\nconst index = rule.field.split('.')[1];\\r\\nconst list = formObj['bjcrxxTable'][index]['jcrxxTable'];\\r\\n\\r\\nlet total = 0; // 初始化总和\\r\\nconsole.log('开始累加继承份额：');\\r\\n\\r\\nlist.forEach((item, index) => {\\r\\n    console.log(`处理继承人 ${index + 1}: ${item.jcrjcfe}`);\\r\\n    \\r\\n    if (type === '百分号') {  // 百分号类型处理\\r\\n        if (item.jcrjcfe.indexOf('%') > -1) {\\r\\n            const value = Number(item.jcrjcfe.split('%')[0]);\\r\\n            total += value; // 累加百分号的数值\\r\\n            console.log(`当前总和（百分号）: ${total}`);\\r\\n        } else {\\r\\n            console.log('错误：请填写符合格式的继承人继承份额');\\r\\n            callback(new Error('请填写符合格式的继承人继承份额'));\\r\\n        }\\r\\n    } else {  // 分数类型处理\\r\\n        if (item.jcrjcfe.indexOf('/') > -1) {\\r\\n            if (total === 0) {  // 第一个分数\\r\\n                total = item.jcrjcfe;\\r\\n                console.log(`初始化总和（分数）: ${total}`);\\r\\n            } else {\\r\\n                const obj = addFractions(parseFraction(total), parseFraction(item.jcrjcfe));\\r\\n                total = `${obj.numerator}/${obj.denominator}`; // 更新总和\\r\\n                console.log(`更新后的总和（分数）: ${total}`);\\r\\n            }\\r\\n        } else {\\r\\n            console.log('错误：请填写符合格式的继承人继承份额');\\r\\n            callback(new Error('请填写符合格式的继承人继承份额'));\\r\\n        }\\r\\n    }\\r\\n});\\r\\n\\r\\nconsole.log(`最终总和: ${total}, 输入值: ${now}, 类型: ${type}`);\\r\\n\\r\\n// 校验总和是否匹配\\r\\nif (type === '百分号') {\\r\\n    if (total !== now) {  // 百分号时，比较总和\\r\\n        console.log('校验失败：继承份额之和与被继承份额不一致');\\r\\n        callback(new Error('继承份额之和与被继承份额不一致，请检查继承份额！'));\\r\\n    } else {\\r\\n        console.log('校验成功');\\r\\n        callback(); // 校验通过\\r\\n    }\\r\\n} else {  // 分数类型时，比较分数总和\\r\\n    if (total !== now) {\\r\\n        console.log('校验失败：继承份额之和与被继承份额不一致');\\r\\n        callback(new Error('继承份额之和与被继承份额不一致，请检查继承份额！'));\\r\\n    } else {\\r\\n        console.log('校验成功');\\r\\n        callback(); // 校验通过\\r\\n    }\\r\\n}\\r\\n\",\"trigger\":\"blur\"}],\"id\":\"QuMKaQBAG\"}],\"id\":\"phK3aXCnx\"}],\"gutter\":40,\"justify\":\"start\",\"align\":\"top\",\"functions\":[],\"field\":\"ljQGcLN_x\"},\"events\":{},\"child\":[],\"rules\":[],\"id\":\"KLUlAe57Y6\"},{\"icon\":\"icon-biaodan\",\"name\":\"可新增表格\",\"platform\":\"all\",\"type\":\"addTable\",\"needSpecial\":false,\"componentName\":\"ANetCanAddTable\",\"props\":{\"functions\":[{\"name\":\"delete事件\",\"value\":\"\",\"key\":\"deletekXPcFTY9h\"},{\"name\":\"add事件\",\"value\":\"\",\"key\":\"addkXPcFTY9h\"}],\"title\":\"继承人信息\",\"deleteText\":\"删除\",\"addText\":\"新增\",\"defaultLine\":0,\"isShowAsTable\":false,\"isShowIndex\":true,\"isSelection\":false,\"isAddByDialog\":false,\"isShowOutBorder\":true,\"isShowInnerBorder\":true,\"innerBorderColor\":\"#000\",\"outerBorderColor\":\"#000\",\"innerWidth\":1,\"outerWidth\":1,\"key\":\"jcrxxTable\",\"isNeedMax\":false,\"isNeedMin\":false,\"showColumns\":\"\",\"min\":1,\"max\":1,\"field\":\"jcrxxTable\"},\"events\":{},\"child\":[{\"child\":[{\"icon\":\"icon-icon-\",\"name\":\"栅格\",\"platform\":\"pc\",\"type\":\"grid\",\"needSpecial\":false,\"props\":{\"columns\":[{\"span\":24,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-ziti\",\"name\":\"文字\",\"platform\":\"all\",\"componentName\":\"a-net-text\",\"needSpecial\":false,\"needRules\":false,\"props\":{\"label\":\"继承份额若为百分数请填写占比%，如50%；继承份额若为分数请填写分子/分母，如1/2\",\"labelWidth\":\"\",\"field\":\"LHP9qd-Qk\",\"modelValue\":\"\",\"tableWidth\":\"\",\"key\":\"\",\"color\":\"#FF0026\",\"fontSize\":\"16px\",\"isTitle\":false,\"fontWeight\":500,\"tipsTitle\":\"\",\"tipsContent\":\"\",\"tipsWidth\":\"\",\"tipsPosition\":\"right\",\"functions\":[{\"name\":\"click事件\",\"value\":\"\",\"key\":\"clickLHP9qd-Qk\"}]},\"events\":{},\"child\":[],\"rules\":[],\"id\":\"B4de3k3-gY\"}],\"id\":\"8vD9vWvmQW\"}],\"gutter\":0,\"justify\":\"start\",\"align\":\"top\",\"functions\":[],\"field\":\"W1Psep7dG\"},\"events\":{},\"child\":[],\"rules\":[],\"id\":\"ceh_vrhwBP\"},{\"icon\":\"icon-icon-\",\"name\":\"栅格\",\"platform\":\"pc\",\"type\":\"grid\",\"needSpecial\":false,\"props\":{\"columns\":[{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-input\",\"name\":\"输入框\",\"componentName\":\"a-net-input\",\"needSpecial\":false,\"platform\":\"all\",\"needRules\":true,\"props\":{\"label\":\"继承人姓名\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"field\":\"jcrxm\",\"key\":\"jcrxm\",\"modelValue\":\"\",\"placeholder\":\"请在此处输入内容\",\"tableWidth\":\"\",\"clearable\":false,\"disabled\":false,\"readonly\":false,\"maxlength\":50,\"default\":\"\",\"isShowLimit\":false,\"showAppendBtn\":false,\"buttonText\":\"\",\"appendBtnColor\":\"#2c8ef1\",\"functions\":[{\"name\":\"input事件\",\"value\":\"console.log(form)\",\"key\":\"inputjcrxm\"},{\"name\":\"foucs事件\",\"value\":\"\",\"key\":\"foucsjcrxm\"},{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changejcrxm\"},{\"name\":\"click事件\",\"value\":\"\",\"key\":\"clickjcrxm\"}],\"id\":\"a05739a3c76a4b28ab561ff1b2014886\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"继承人姓名不能为空\",\"trigger\":\"blur\"}],\"id\":\"8q7fkI_Yd\"}],\"id\":\"VDoDD4QMRj\"},{\"span\":12,\"child\":[{\"icon\":\"icon-input\",\"name\":\"输入框\",\"componentName\":\"a-net-input\",\"needSpecial\":false,\"platform\":\"all\",\"needRules\":true,\"props\":{\"label\":\"继承人身份证号码\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"field\":\"jcrsfzhm\",\"key\":\"jcrsfzhm\",\"modelValue\":\"\",\"placeholder\":\"请在此处输入内容\",\"tableWidth\":\"\",\"clearable\":false,\"disabled\":false,\"readonly\":false,\"maxlength\":50,\"default\":\"\",\"isShowLimit\":false,\"showAppendBtn\":false,\"buttonText\":\"\",\"appendBtnColor\":\"#2c8ef1\",\"functions\":[{\"name\":\"input事件\",\"value\":\"console.log(form)\",\"key\":\"inputjcrsfzhm\"},{\"name\":\"foucs事件\",\"value\":\"\",\"key\":\"foucsjcrsfzhm\"},{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changejcrsfzhm\"},{\"name\":\"click事件\",\"value\":\"\",\"key\":\"clickjcrsfzhm\"}],\"id\":\"15daf9ac517442f0815c7dcec7f6ec08\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"继承人身份证号码不能为空\",\"trigger\":\"blur\"},{\"validatorStr\":\"function getValueByPath(obj, path) {\\r\\n    return path.split('.').reduce((acc, part) => {\\r\\n        if (acc && typeof acc === 'object') {\\r\\n            const index = parseInt(part);\\r\\n            if (!isNaN(index) && Array.isArray(acc)) {\\r\\n                return acc[index];\\r\\n            }\\r\\n            return acc[part];\\r\\n        }\\r\\n        return undefined;\\r\\n    }, obj);\\r\\n}\\r\\n\\r\\nconst jcrsfzhmValue = getValueByPath(formObj, rule.field);\\r\\nconst jcrposfzhmPath = rule.field.replace('jcrsfzhm', 'jcrposfzhm');\\r\\nconst jcrposfzhmValue = getValueByPath(formObj, jcrposfzhmPath);\\r\\n\\r\\nif (jcrsfzhmValue === jcrposfzhmValue) {\\r\\n    callback(new Error(\\\"继承人身份证号码和继承人配偶身份证号码一致\\\"))\\r\\n} else {\\r\\n    callback()\\r\\n}\\r\\n\\r\\n\",\"trigger\":\"blur\"},{\"pattern\":\"^[1-9]\\\\d{7}((0\\\\d)|(1[0-2]))(([0|1|2]\\\\d)|3[0-1])\\\\d{3}$|^[1-9]\\\\d{5}[1-9]\\\\d{3}((0\\\\d)|(1[0-2]))(([0|1|2]\\\\d)|3[0-1])((\\\\d{4})|\\\\d{3}[xX])$\",\"message\":\"请输入正确的身份证号码\",\"trigger\":\"blur\"}],\"id\":\"xQTLbjt_B\"}],\"offset\":0,\"push\":0,\"pull\":0,\"id\":\"T9vlzErO4D\"},{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-input\",\"name\":\"输入框\",\"componentName\":\"a-net-input\",\"needSpecial\":false,\"platform\":\"all\",\"needRules\":true,\"props\":{\"label\":\"继承人手机号码\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"field\":\"jcrsjhm\",\"key\":\"jcrsjhm\",\"modelValue\":\"\",\"placeholder\":\"请在此处输入内容\",\"tableWidth\":\"\",\"clearable\":false,\"disabled\":false,\"readonly\":false,\"maxlength\":50,\"default\":\"\",\"isShowLimit\":false,\"showAppendBtn\":false,\"buttonText\":\"\",\"appendBtnColor\":\"#2c8ef1\",\"functions\":[{\"name\":\"input事件\",\"value\":\"console.log(form)\",\"key\":\"inputjcrsjhm\"},{\"name\":\"foucs事件\",\"value\":\"\",\"key\":\"foucsjcrsjhm\"},{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changejcrsjhm\"},{\"name\":\"click事件\",\"value\":\"\",\"key\":\"clickjcrsjhm\"}],\"id\":\"b6443188ae2a4caebf140afe10479626\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"继承人手机号码不能为空\",\"trigger\":\"blur\"},{\"pattern\":\"^1[3|4|5|6|7|8|9][0-9]\\\\d{8}$\",\"message\":\"请输入正确的手机号\",\"trigger\":\"blur\"}],\"id\":\"EOKXjVXxr\"}],\"id\":\"D3gcYyCGs\"},{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-input\",\"name\":\"输入框\",\"componentName\":\"a-net-input\",\"needSpecial\":false,\"platform\":\"all\",\"needRules\":true,\"props\":{\"label\":\"继承人继承份额\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"field\":\"jcrjcfe\",\"key\":\"jcrjcfe\",\"modelValue\":\"\",\"placeholder\":\"请在此处输入内容\",\"tableWidth\":\"\",\"clearable\":false,\"disabled\":false,\"readonly\":false,\"maxlength\":50,\"default\":\"\",\"isShowLimit\":false,\"showAppendBtn\":false,\"buttonText\":\"\",\"appendBtnColor\":\"#2c8ef1\",\"functions\":[{\"name\":\"input事件\",\"value\":\"console.log(form)\",\"key\":\"inputjcrjcfe\"},{\"name\":\"foucs事件\",\"value\":\"\",\"key\":\"foucsjcrjcfe\"},{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changejcrjcfe\"},{\"name\":\"click事件\",\"value\":\"\",\"key\":\"clickjcrjcfe\"}],\"id\":\"c37239446f484abd9d9c71ad9524fbfa\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"继承人继承份额不能为空\",\"trigger\":\"blur\"}],\"id\":\"au__4hXoq\"}],\"id\":\"77vzw3oHj\"},{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-danxuankuang\",\"name\":\"单选框\",\"platform\":\"all\",\"componentName\":\"a-net-radio\",\"needSpecial\":false,\"key\":\"\",\"needRules\":true,\"props\":{\"label\":\"继承人是否有代理人\",\"tableWidth\":\"\",\"field\":\"jcrsfydlr\",\"modelValue\":\"\",\"disabled\":false,\"labelWidth\":\"\",\"labelHeight\":\"\",\"placeholder\":\"请选择\",\"optionKey\":\"\",\"functions\":[{\"name\":\"change事件\",\"value\":\"const list = form.formObj['bjcrxxTable']\\r\\nconsole.log(list)\\r\\nlist.forEach((item,index)=>{\\r\\n    item.jcrxxTable.forEach((j,jIndex)=>{\\r\\n        if(j.jcrsfydlr==='有'){\\r\\n        form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.jcrdlrxx'],[true])\\r\\n    }else{\\r\\n       form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.jcrdlrxx'],[false])\\r\\n    }\\r\\n    })\\r\\n})\",\"key\":\"changejcrsfydlr\"}],\"options\":[{\"value\":\"有\",\"label\":\"有\",\"disabled\":false},{\"value\":\"无\",\"label\":\"无\",\"disabled\":false}],\"key\":\"jcrsfydlr\",\"id\":\"02686ff80dc142808094dae024480ccc\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"继承人是否有代理人不能为空\",\"trigger\":\"blur\"}],\"id\":\"HIbbyVRy_\"}],\"id\":\"4pi8HoLP3\"},{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-select\",\"name\":\"下拉框\",\"platform\":\"all\",\"componentName\":\"a-net-select\",\"needSpecial\":false,\"needRules\":true,\"props\":{\"label\":\"继承人代理人信息\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"tableWidth\":\"\",\"field\":\"jcrdlrxx\",\"disabled\":false,\"default\":\"\",\"modelValue\":\"\",\"optionKey\":\"\",\"key\":\"jcrdlrxx\",\"placeholder\":\"请选择\",\"functions\":[{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changejcrdlrxx\"}],\"options\":[{\"value\":\"1\",\"label\":\"选项一\",\"disabled\":false},{\"value\":\"2\",\"label\":\"选项二\",\"disabled\":false}],\"id\":\"b6df8b24ac0a43feb095d389faaab5d1\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"继承人代理人信息不能为空\",\"trigger\":\"blur\"}],\"id\":\"4Rkr_cm3G\"}],\"id\":\"mteaMgtet\"},{\"span\":24,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-danxuankuang\",\"name\":\"单选框\",\"platform\":\"all\",\"componentName\":\"a-net-radio\",\"needSpecial\":false,\"key\":\"\",\"needRules\":true,\"props\":{\"label\":\"继承人婚姻状况\",\"tableWidth\":\"\",\"field\":\"jcrhyzk\",\"modelValue\":\"\",\"disabled\":false,\"labelWidth\":\"\",\"labelHeight\":\"\",\"placeholder\":\"请选择\",\"optionKey\":\"\",\"functions\":[{\"name\":\"change事件\",\"value\":\"const list = form.formObj['bjcrxxTable']\\r\\nconsole.log(list)\\r\\nlist.forEach((item,index)=>{\\r\\n    item.jcrxxTable.forEach((j,jIndex)=>{\\r\\n        if(j.jcrhyzk==='已婚'){\\r\\n        form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.Ugo3aIT-J','bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.sINakINs_Self'],[true,true])\\r\\n    }else{\\r\\n        form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.Ugo3aIT-J','bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.sINakINs_Self'],[false,false])\\r\\n\\r\\n    }\\r\\n    })\\r\\n})\\r\\n    \\r\\n \",\"key\":\"changejcrhyzk\"}],\"options\":[{\"value\":\"未婚\",\"label\":\"未婚\",\"disabled\":false},{\"value\":\"已婚\",\"label\":\"已婚\",\"disabled\":false},{\"label\":\"离异\",\"value\":\"离异\",\"disabled\":false},{\"label\":\"丧偶\",\"value\":\"丧偶\",\"disabled\":false}],\"key\":\"jcrhyzk\",\"id\":\"b47574e7b0ae4ec583088045c4f7b49d\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"继承人婚姻状况不能为空\",\"trigger\":\"blur\"}],\"id\":\"5UefEY9Zc\"}],\"id\":\"4TbszCtkI\"}],\"gutter\":40,\"justify\":\"start\",\"align\":\"top\",\"functions\":[],\"field\":\"j2tzvgMOD\"},\"events\":{},\"child\":[],\"rules\":[],\"id\":\"rCv2tC74Wq\"},{\"icon\":\"icon-fengexian\",\"name\":\"分割线\",\"platform\":\"all\",\"componentName\":\"a-net-divider\",\"needSpecial\":false,\"needRules\":false,\"props\":{\"label\":\"分割线\",\"modelValue\":\"分割线\",\"key\":\"\",\"contentPosition\":\"center\",\"functions\":[{\"name\":\"click事件\",\"value\":\"\",\"key\":\"clickUgo3aIT-J\"}],\"field\":\"Ugo3aIT-J\"},\"events\":{},\"child\":[],\"rules\":[],\"id\":\"3-46je85Oe\"},{\"icon\":\"icon-biaodan\",\"name\":\"表单域\",\"platform\":\"all\",\"type\":\"formArea\",\"needSpecial\":false,\"componentName\":\"ANetFormArea\",\"props\":{\"bgColor\":\"#dd4b39\",\"title\":\"继承人配偶信息\",\"titleSize\":22,\"titleColor\":\"#0E0D0D\",\"barColor\":\"\",\"isHidden\":false,\"functions\":[],\"isShowButton\":true,\"arrowColor\":\"#000000\",\"show\":true,\"field\":\"sINakINs_\"},\"events\":{},\"child\":[{\"icon\":\"icon-icon-\",\"name\":\"栅格\",\"platform\":\"pc\",\"type\":\"grid\",\"needSpecial\":false,\"props\":{\"columns\":[{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-input\",\"name\":\"输入框\",\"componentName\":\"a-net-input\",\"needSpecial\":false,\"platform\":\"all\",\"needRules\":true,\"props\":{\"label\":\"继承人配偶姓名\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"field\":\"jcrpoxm\",\"key\":\"jcrpoxm\",\"modelValue\":\"\",\"placeholder\":\"请在此处输入内容\",\"tableWidth\":\"\",\"clearable\":false,\"disabled\":false,\"readonly\":false,\"maxlength\":50,\"default\":\"\",\"isShowLimit\":false,\"showAppendBtn\":false,\"buttonText\":\"\",\"appendBtnColor\":\"#2c8ef1\",\"functions\":[{\"name\":\"input事件\",\"value\":\"console.log(form)\",\"key\":\"inputjcrpoxm\"},{\"name\":\"foucs事件\",\"value\":\"\",\"key\":\"foucsjcrpoxm\"},{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changejcrpoxm\"},{\"name\":\"click事件\",\"value\":\"\",\"key\":\"clickjcrpoxm\"}],\"id\":\"62c3de57f81e4b4ba3346f41345e0455\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"继承人配偶姓名不能为空\",\"trigger\":\"blur\"}],\"id\":\"VYxNMqGuR\"}],\"id\":\"bpb3R26_f4\"},{\"span\":12,\"child\":[{\"icon\":\"icon-input\",\"name\":\"输入框\",\"componentName\":\"a-net-input\",\"needSpecial\":false,\"platform\":\"all\",\"needRules\":true,\"props\":{\"label\":\"继承人配偶身份证号码\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"field\":\"jcrposfzhm\",\"key\":\"jcrposfzhm\",\"modelValue\":\"\",\"placeholder\":\"请在此处输入内容\",\"tableWidth\":\"\",\"clearable\":false,\"disabled\":false,\"readonly\":false,\"maxlength\":50,\"default\":\"\",\"isShowLimit\":false,\"showAppendBtn\":false,\"buttonText\":\"\",\"appendBtnColor\":\"#2c8ef1\",\"functions\":[{\"name\":\"input事件\",\"value\":\"console.log(form)\",\"key\":\"inputjcrposfzhm\"},{\"name\":\"foucs事件\",\"value\":\"\",\"key\":\"foucsjcrposfzhm\"},{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changejcrposfzhm\"},{\"name\":\"click事件\",\"value\":\"\",\"key\":\"clickjcrposfzhm\"}],\"id\":\"3966e012574a407a8abcd894ea37bbef\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"继承人配偶身份证号码不能为空\",\"trigger\":\"blur\"},{\"validatorStr\":\"    const indexsList = rule.field.match(/\\\\b\\\\d+\\\\b/g).map(Number);\\r\\n    const jcrsfzhm = formObj.bjcrxxTable[indexsList[0]].jcrxxTable[indexsList[1]].jcrsfzhm\\r\\n    const jcrposfzhm = formObj.bjcrxxTable[indexsList[0]].jcrxxTable[indexsList[1]].jcrposfzhm;\\r\\n\\r\\n    if (jcrsfzhm === jcrposfzhm) {\\r\\n        callback(new Error(\\\"继承人身份证号码和继承人配偶身份证号码一致\\\"));\\r\\n    } else {\\r\\n        callback()\\r\\n    }\",\"trigger\":\"blur\"},{\"pattern\":\"^[1-9]\\\\d{7}((0\\\\d)|(1[0-2]))(([0|1|2]\\\\d)|3[0-1])\\\\d{3}$|^[1-9]\\\\d{5}[1-9]\\\\d{3}((0\\\\d)|(1[0-2]))(([0|1|2]\\\\d)|3[0-1])((\\\\d{4})|\\\\d{3}[xX])$\",\"message\":\"请输入正确的身份证号码\",\"trigger\":\"blur\"}],\"id\":\"JNTJ3XjL_\"}],\"offset\":0,\"push\":0,\"pull\":0,\"id\":\"R2eGIwS977\"},{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-input\",\"name\":\"输入框\",\"componentName\":\"a-net-input\",\"needSpecial\":false,\"platform\":\"all\",\"needRules\":true,\"props\":{\"label\":\"继承人配偶手机号\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"field\":\"jcrposjh\",\"key\":\"jcrposjh\",\"modelValue\":\"\",\"placeholder\":\"请在此处输入内容\",\"tableWidth\":\"\",\"clearable\":false,\"disabled\":false,\"readonly\":false,\"maxlength\":50,\"default\":\"\",\"isShowLimit\":false,\"showAppendBtn\":false,\"buttonText\":\"\",\"appendBtnColor\":\"#2c8ef1\",\"functions\":[{\"name\":\"input事件\",\"value\":\"console.log(form)\",\"key\":\"inputjcrposjh\"},{\"name\":\"foucs事件\",\"value\":\"\",\"key\":\"foucsjcrposjh\"},{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changejcrposjh\"},{\"name\":\"click事件\",\"value\":\"\",\"key\":\"clickjcrposjh\"}],\"id\":\"ec7040cbcc8348e3913de4425d939f9b\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"继承人配偶手机号不能为空\",\"trigger\":\"blur\"},{\"pattern\":\"^1[3|4|5|6|7|8|9][0-9]\\\\d{8}$\",\"message\":\"请输入正确的手机号\",\"trigger\":\"blur\"}],\"id\":\"d33e14fgu\"}],\"id\":\"9dGsUpz7O\"},{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-danxuankuang\",\"name\":\"单选框\",\"platform\":\"all\",\"componentName\":\"a-net-radio\",\"needSpecial\":false,\"key\":\"\",\"needRules\":true,\"props\":{\"label\":\"继承人配偶签名方式\",\"tableWidth\":\"\",\"field\":\"jcrpoqmfs\",\"modelValue\":\"\",\"disabled\":false,\"labelWidth\":\"\",\"labelHeight\":\"\",\"placeholder\":\"请选择\",\"optionKey\":\"\",\"functions\":[{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changejcrpoqmfs\"}],\"options\":[{\"value\":\"现场签名\",\"label\":\"现场签名\",\"disabled\":false},{\"value\":\"线上签名\",\"label\":\"线上签名\",\"disabled\":false}],\"key\":\"jcrpoqmfs\",\"id\":\"857e9af2b40f4270b15aed460dd546fc\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"继承人配偶签名方式不能为空\",\"trigger\":\"blur\"}],\"id\":\"hdOQXKDHC\"}],\"id\":\"I2lL8eDV4\"},{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-danxuankuang\",\"name\":\"单选框\",\"platform\":\"all\",\"componentName\":\"a-net-radio\",\"needSpecial\":false,\"key\":\"\",\"needRules\":true,\"props\":{\"label\":\"继承人配偶是否登薄\",\"tableWidth\":\"\",\"field\":\"jcrposfdb\",\"modelValue\":\"\",\"disabled\":false,\"labelWidth\":\"\",\"labelHeight\":\"\",\"placeholder\":\"请选择\",\"optionKey\":\"\",\"functions\":[{\"name\":\"change事件\",\"value\":\"const list = form.formObj['bjcrxxTable']\\r\\nconsole.log(list)\\r\\nlist.forEach((item,index)=>{\\r\\n    item.jcrxxTable.forEach((j,jIndex)=>{\\r\\n        if(j.jcrposfdb==='登簿'){\\r\\n        form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.jcrpodbfe'],[true])\\r\\n    }else{\\r\\n       form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.jcrpodbfe'],[false])\\r\\n    }\\r\\n    })\\r\\n})\",\"key\":\"changejcrposfdb\"}],\"options\":[{\"value\":\"登簿\",\"label\":\"登簿\",\"disabled\":false},{\"value\":\"不登簿\",\"label\":\"不登簿\",\"disabled\":false}],\"key\":\"jcrposfdb\",\"id\":\"1e7340f65f3f4f5ca4d36bd0b472cbe6\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"继承人配偶是否登薄不能为空\",\"trigger\":\"blur\"}],\"id\":\"p2IGSlxCi\"}],\"id\":\"0TnIFF8zJ\"},{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-input\",\"name\":\"输入框\",\"componentName\":\"a-net-input\",\"needSpecial\":false,\"platform\":\"all\",\"needRules\":true,\"props\":{\"label\":\"继承人配偶登簿份额\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"field\":\"jcrpodbfe\",\"key\":\"jcrpozfe\",\"modelValue\":\"\",\"placeholder\":\"请在此处输入内容\",\"tableWidth\":\"\",\"clearable\":false,\"disabled\":false,\"readonly\":false,\"maxlength\":50,\"default\":\"\",\"isShowLimit\":false,\"showAppendBtn\":false,\"buttonText\":\"\",\"appendBtnColor\":\"#2c8ef1\",\"functions\":[{\"name\":\"input事件\",\"value\":\"console.log(form)\",\"key\":\"inputjcrpozfe\"},{\"name\":\"foucs事件\",\"value\":\"\",\"key\":\"foucsjcrpozfe\"},{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changejcrpozfe\"},{\"name\":\"click事件\",\"value\":\"\",\"key\":\"clickjcrpozfe\"}],\"id\":\"ecaaf649f79e4ca4ae3c5e105bdd7898\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"继承人配偶总份额不能为空\",\"trigger\":\"blur\"}],\"id\":\"VGdkAOvhb\"}],\"id\":\"QrKaOuKGCd\"},{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-danxuankuang\",\"name\":\"单选框\",\"platform\":\"all\",\"componentName\":\"a-net-radio\",\"needSpecial\":false,\"key\":\"\",\"needRules\":true,\"props\":{\"label\":\"继承人配偶是否有代理人\",\"tableWidth\":\"\",\"field\":\"jcrposfydlr\",\"modelValue\":\"\",\"disabled\":false,\"labelWidth\":\"\",\"labelHeight\":\"\",\"placeholder\":\"请选择\",\"optionKey\":\"\",\"functions\":[{\"name\":\"change事件\",\"value\":\"const list = form.formObj['bjcrxxTable']\\r\\nconsole.log(list)\\r\\nlist.forEach((item,index)=>{\\r\\n    item.jcrxxTable.forEach((j,jIndex)=>{\\r\\n        if(j.jcrposfydlr==='有'){\\r\\n        form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.jcrpodlrxx'],[true])\\r\\n    }else{\\r\\n       form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.jcrpodlrxx'],[false])\\r\\n    }\\r\\n    })\\r\\n})\",\"key\":\"changejcrposfydlr\"}],\"options\":[{\"value\":\"有\",\"label\":\"有\",\"disabled\":false},{\"value\":\"无\",\"label\":\"无\",\"disabled\":false}],\"key\":\"jcrposfydlr\",\"id\":\"695f17e8662441538a1ec1022cc2b8b5\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"继承人配偶是否有代理人不能为空\",\"trigger\":\"blur\"}],\"id\":\"UUj8o9E3i\"}],\"id\":\"LLUqqo2Uw\"},{\"span\":12,\"offset\":0,\"push\":0,\"pull\":0,\"child\":[{\"icon\":\"icon-select\",\"name\":\"下拉框\",\"platform\":\"all\",\"componentName\":\"a-net-select\",\"needSpecial\":false,\"needRules\":true,\"props\":{\"label\":\"继承人配偶代理人信息\",\"labelWidth\":\"\",\"labelHeight\":\"\",\"tableWidth\":\"\",\"field\":\"jcrpodlrxx\",\"disabled\":false,\"default\":\"\",\"modelValue\":\"\",\"optionKey\":\"\",\"key\":\"jcrpodlrxx\",\"placeholder\":\"请选择\",\"functions\":[{\"name\":\"change事件\",\"value\":\"\",\"key\":\"changejcrpodlrxx\"}],\"options\":[{\"value\":\"1\",\"label\":\"选项一\",\"disabled\":false},{\"value\":\"2\",\"label\":\"选项二\",\"disabled\":false}],\"id\":\"d0cff61c11694cf3bb8af0bc31926228\"},\"events\":{},\"child\":[],\"rules\":[{\"required\":true,\"message\":\"继承人配偶代理人信息不能为空\",\"trigger\":\"blur\"}],\"id\":\"bA81beSBn\"}],\"id\":\"6NMiStMz7\"}],\"gutter\":40,\"justify\":\"start\",\"align\":\"top\",\"functions\":[],\"field\":\"B3c4jUJng\"},\"events\":{},\"child\":[],\"rules\":[],\"id\":\"ELWdCzdGAc\"}],\"rules\":[],\"id\":\"F8f1lvVyCW\"}]}],\"rules\":[],\"id\":\"jJUgPc0ul5\"}]}],\"rules\":[],\"id\":\"l5rI0osdBx\"}],\"rules\":[],\"id\":\"MjP1eqr4pM\"}],\"formAttribute\":{\"inline\":false,\"disabled\":false,\"tableName\":\"demoFormName\",\"labelWidth\":\"200px\",\"labelPosition\":\"right\"},\"beforeFunction\":\"const list = form.formObj['bjcrxxTable']\\r\\nconsole.log(list)\\r\\nlist.forEach((item,index)=>{\\r\\n    item.jcrxxTable.forEach((j,jIndex)=>{\\r\\n        if(j.jcrhyzk==='已婚'){\\r\\n        form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.Ugo3aIT-J','bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.sINakINs_Self'],[true,true])\\r\\n    }else{\\r\\n        form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.Ugo3aIT-J','bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.sINakINs_Self'],[false,false])\\r\\n\\r\\n    }\\r\\n     if(j.jcrsfydlr==='有'){\\r\\n        form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.jcrdlrxx'],[true])\\r\\n    }else{\\r\\n       form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.jcrdlrxx'],[false])\\r\\n    }\\r\\n       if(j.jcrposfdb==='登簿'){\\r\\n        form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.jcrpodbfe'],[true])\\r\\n    }else{\\r\\n       form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.jcrpodbfe'],[false])\\r\\n    }\\r\\n      if(j.jcrposfydlr==='有'){\\r\\n        form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.jcrpodlrxx'],[true])\\r\\n    }else{\\r\\n       form.updateFormVisible(['bjcrxxTable.'+index+'.jcrxxTable.'+jIndex+'.jcrpodlrxx'],[false])\\r\\n    }\\r\\n    })\\r\\n})\\r\\n    \",\"submitFunction\":\"{}\"}" ;
        JSONObject jsonObject = JSON.parseObject(string);
        System.out.println(jsonObject);
    }


    @Test
    void testConvertToTemplateData() {
        // 1. 直接对象
        String json1 = "{\"fwbh\":\"001\",\"xmmc\":\"项目1\"}";

// 2. data数组
        String json2 = "{\"data\":[{\"fwbh\":\"001\",\"xmmc\":\"项目1\"},{\"fwbh\":\"002\",\"xmmc\":\"项目2\"}]}";

// 3. data对象
        String json3 = "{\"data\":{\"fwbh\":\"001\",\"xmmc\":\"项目1\"}}";

// 4. 深层嵌套
        String json4 = "{\"result\":{\"data\":{\"fwbh\":\"001\",\"xmmc\":\"项目1\"}}}";

// 处理数据
        Object templateData = dataMappingService.convertToTemplateData(json2); // 或 json2, json3, json4
        Object formData = dataMappingService.convertToFormData(templateData);
        assertNotNull(formData);
        System.out.println("第三方：(编号)fwbh，（名称）xmmc");
        System.out.println("模板：(编号)contractNo，（名称）projectName");
        System.out.println("表单：(编号)no，（名称）name");
        System.out.println("第三方转模板： " + templateData.toString());
        System.out.println("模板转表单：" + formData);
    }
}

