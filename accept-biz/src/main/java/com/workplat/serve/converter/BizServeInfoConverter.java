package com.workplat.serve.converter;

import com.workplat.accept.business.serve.entity.BizServeInfo;
import com.workplat.accept.business.serve.vo.BizServeInfoVo;
import com.workplat.gss.common.core.converter.BaseConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class BizServeInfoConverter implements BaseConverter<BizServeInfo, BizServeInfoVo> {

    @Autowired
    private BizServeMethodConverter bizServeMethodConverter;

    @Override
    public BizServeInfoVo convert(BizServeInfo source) {
        BizServeInfoVo bizServeInfoVo=new BizServeInfoVo();
        if ( source ==null){
            return null;
        }
        if (!source.getMethodList().isEmpty()){
            bizServeInfoVo.setMethodList(bizServeMethodConverter.convert(source.getMethodList()));
        }
        bizServeInfoVo.setId(source.getId());
        bizServeInfoVo.setName(source.getName());
        bizServeInfoVo.setType(source.getType());
        bizServeInfoVo.setGuideType(source.getGuideType());
        bizServeInfoVo.setGuideUrl(source.getGuideUrl());
        bizServeInfoVo.setEnable(source.isEnable());
        bizServeInfoVo.setDescription(source.getDescription());
        bizServeInfoVo.setCode(source.getCode());
        bizServeInfoVo.setThirdParty(source.isThirdParty());
        return bizServeInfoVo ;
    }


}
