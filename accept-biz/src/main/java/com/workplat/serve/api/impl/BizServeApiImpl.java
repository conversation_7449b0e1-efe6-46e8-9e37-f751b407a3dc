package com.workplat.serve.api.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.util.RedisUtil;
import com.workplat.serve.api.BizServeApi;
import com.workplat.accept.business.serve.entity.BizServeInfo;
import com.workplat.accept.business.serve.entity.BizServeMethod;
import com.workplat.accept.business.serve.vo.BizServeInfoVo;
import com.workplat.gss.common.core.dto.DictModel;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.common.dict.service.SysDictionarySubService;
import com.workplat.serve.converter.BizServeInfoConverter;
import com.workplat.serve.dto.*;
import com.workplat.serve.importData.TechTalentApartExcel;
import com.workplat.serve.importData.TechTalentApartExcelListener;
import com.workplat.serve.service.BizServeInfoService;
import com.workplat.serve.service.BizServeMethodService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;

@Slf4j
@RestController
public class BizServeApiImpl implements BizServeApi {

    @Autowired
    private BizServeInfoService bizServeInfoService;

    @Autowired
    private BizServeInfoConverter bizServeInfoConverter;

    @Autowired
    private BizServeMethodService bizServeMethodService;

    @Autowired
    private SysDictionarySubService sysDictionarySubService;
    @Autowired
    private RedisUtil redisUtil;

    private static String infoType = "app";

    private static String methodType = "XSB";

    //认证等级
    private static String RZDJ = "serve_certification_level";

    //服务类型
    private static String FWLX = "serve_type";

    //办事指南类型
    private static String BSZNLX = "guide_type";

    //渠道类型
    private static String QDLX = "serve_method";
    private static String redisKey = "serve_method_default";


    @Override
    public ResponseData<Page<BizServeInfoVo>> queryListByName(KeyDto keyDto, PageableDTO pageDTO) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("LIKE(name)", "");
        if (keyDto != null) {
            String keyWord = keyDto.getName();
            String enable = keyDto.getEnable();
            String thirdParty = keyDto.getThirdParty();
            if (StringUtil.isNotBlank(keyWord)) {
                map.put("LIKE(name)", keyWord);
            }
            if (StringUtil.isNotBlank(enable)) {
                if ("1".equals(enable)) {
                    map.put("=(enable)", true);
                }
                if ("0".equals(enable)) {
                    map.put("=(enable)", false);
                }
            }
            if (StringUtil.isNotBlank(thirdParty)) {
                if ("1".equals(thirdParty)) {
                    map.put("=(thirdParty)", true);
                }
                if ("0".equals(thirdParty)) {
                    map.put("=(thirdParty)", false);
                }
            }
        }

        Page<BizServeInfo> bizServeInfos = bizServeInfoService.queryForPage(map, pageDTO.convertPageable());

        return ResponseData.success("获取成功", bizServeInfoConverter.convert(bizServeInfos));
    }

    @Override
    public ResponseData<BizServeInfoVo> getById(String id) {
        BizServeInfo bizServeInfo = bizServeInfoService.queryById(id);
        return ResponseData.success("获取成功", bizServeInfoConverter.convert(bizServeInfo));
    }

    @Override
    public ResponseData<List<BizServeInfoVo>> getByIds(String ids) {
        String[] idList = ids.split(",");
        List<BizServeInfo> bizServeInfos = bizServeInfoService.queryByIds(idList);
        return ResponseData.success("获取成功", bizServeInfoConverter.convert(bizServeInfos));
    }

    @Override
    public ResponseData deleteInfo(BizServeInfoDto bizServeInfoDto) {
        if (!bizServeInfoDto.getMethodList().isEmpty()) {
            for (BizServeMethodDto methodDto : bizServeInfoDto.getMethodList()) {
                bizServeMethodService.deleteById(methodDto.getId());
            }
        }
        bizServeInfoService.deleteById(bizServeInfoDto.getId());
        return ResponseData.success("删除成功");
    }

    @Override
    public ResponseData updateById(BizServeInfoDto bizServeInfoDto) {
        if(StringUtils.isNotBlank(bizServeInfoDto.getId())){
            bizServeMethodService.deleteByParams(MapUtil.<String, Object>builder().put("=(serve.id)", bizServeInfoDto.getId()).build());
        }
        BizServeInfo bizServeInfo = bizServeInfoService.queryById(bizServeInfoDto.getId());
        List<BizServeMethod> list = Lists.newArrayList();
        bizServeInfo.setCode(bizServeInfoDto.getCode());
        bizServeInfo.setName(bizServeInfoDto.getName());
        bizServeInfo.setDescription(bizServeInfoDto.getDescription());
        bizServeInfo.setType(bizServeInfoDto.getType());
        bizServeInfo.setEnable(bizServeInfoDto.getEnable());
        bizServeInfo.setThirdParty(bizServeInfoDto.getThirdParty());
        bizServeInfo.setGuideType(bizServeInfoDto.getGuideType());
        bizServeInfo.setGuideUrl(bizServeInfoDto.getGuideUrl());
        if (!bizServeInfoDto.getMethodList().isEmpty()) {
            for (BizServeMethodDto methodDto : bizServeInfoDto.getMethodList()) {
                BizServeMethod method = new BizServeMethod();
                method.setType(methodDto.getType());
                method.setContent(methodDto.getContent());
                method.setCertificationLevel(methodDto.getCertificationLevel());
                method.setIconId(methodDto.getIconId());
                method.setDescription(methodDto.getDescription());
                method.setServe(bizServeInfo);
                method.setSort(methodDto.getSort());
                list.add(method);
                bizServeMethodService.save(method);
            }
        }
        bizServeInfo.setMethodList(list);
        bizServeInfoService.save(bizServeInfo);
        return ResponseData.success("更新成功");
    }

    @Override
    public ResponseData addInfo(BizServeInfoDto bizServeInfoDto) {
        BizServeInfo bizServeInfo = new BizServeInfo();
        BeanUtil.copyProperties(bizServeInfoDto, bizServeInfo);
        List<BizServeMethod> methodList = Lists.newArrayList();
        if (!bizServeInfoDto.getMethodList().isEmpty()) {
            for (BizServeMethodDto methodDto : bizServeInfoDto.getMethodList()) {
                BizServeMethod method = new BizServeMethod();
                BeanUtil.copyProperties(methodDto,method);
                method.setServe(bizServeInfo);
                methodList.add(method);
            }
        }
        bizServeInfoService.save(bizServeInfo);
        if (!methodList.isEmpty()){
            bizServeMethodService.saveList(methodList);
        }
        return ResponseData.success("新增成功");
    }


    @Override
    public ResponseData importData(MultipartFile file) {

        try {
            List<TechTalentApartExcel> techTalentApartExcels = EasyExcel.read(file.getInputStream(), new TechTalentApartExcelListener()).head(TechTalentApartExcel.class).sheet(0).headRowNumber(1).doReadSync();
            if (CollectionUtils.isNotEmpty(techTalentApartExcels)) {

                for (TechTalentApartExcel techTalentApartExcel : techTalentApartExcels) {

                    if (techTalentApartExcel != null) {
                        List<DictModel> dictCacheListRZDJ = sysDictionarySubService.getDictCacheListByCode(RZDJ);
                        BizServeInfo bizServeInfo = new BizServeInfo();
                        bizServeInfo.setName(techTalentApartExcel.getYymc());
                        bizServeInfo.setType(infoType);
                        BizServeMethod bizServeMethod = new BizServeMethod();
                        bizServeMethod.setContent(techTalentApartExcel.getYylj());
                        bizServeMethod.setServe(bizServeInfo);
                        for (DictModel dictModel : dictCacheListRZDJ) {
                            if (dictModel.getValue().equals(techTalentApartExcel.getRzdj())) {
                                bizServeMethod.setCertificationLevel(Integer.parseInt(dictModel.getKey()));
                            }
                        }
                        bizServeMethod.setType(methodType);
                        BizServeInfo save = bizServeInfoService.save(bizServeInfo);
                        if (save != null) {
                            bizServeMethodService.save(bizServeMethod);
                        }

                    }
                }

            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return ResponseData.success("导入成功");
    }

    @Override
    public ResponseData getRZDJ() {

        List<DictModel> dictCacheListByCode = sysDictionarySubService.getDictCacheListByCode(RZDJ);
        JSONArray list = new JSONArray();
        for (DictModel dictModel : dictCacheListByCode) {
            JSONObject rtn = new JSONObject();
            rtn.put("key", dictModel.getKey());
            rtn.put("value", dictModel.getValue());
            list.add(rtn);
        }
        return ResponseData.success(list);
    }

    @Override
    public ResponseData getFWLX() {

        List<DictModel> dictCacheListByCode = sysDictionarySubService.getDictCacheListByCode(FWLX);
        JSONArray list = new JSONArray();
        for (DictModel dictModel : dictCacheListByCode) {
            JSONObject rtn = new JSONObject();
            rtn.put("key", dictModel.getKey());
            rtn.put("value", dictModel.getValue());
            list.add(rtn);
        }
        return ResponseData.success(list);
    }

    @Override
    public ResponseData getBSZNLX() {

        List<DictModel> dictCacheListByCode = sysDictionarySubService.getDictCacheListByCode(BSZNLX);
        JSONArray list = new JSONArray();
        for (DictModel dictModel : dictCacheListByCode) {
            JSONObject rtn = new JSONObject();
            rtn.put("key", dictModel.getKey());
            rtn.put("value", dictModel.getValue());
            list.add(rtn);
        }
        return ResponseData.success(list);
    }

    @Override
    public ResponseData getQDLX() {

        List<DictModel> dictCacheListByCode = sysDictionarySubService.getDictCacheListByCode(QDLX);
        JSONArray list = new JSONArray();
        for (DictModel dictModel : dictCacheListByCode) {
            JSONObject rtn = new JSONObject();
            rtn.put("key", dictModel.getKey());
            rtn.put("value", dictModel.getValue());
            list.add(rtn);
        }
        return ResponseData.success(list);
    }

    @Override
    public ResponseData plqy(IdsDto idsDto) {
        String ids = idsDto.getIds();
        String[] split = ids.split(",");
        for (String s : split) {
            BizServeInfo bizServeInfo = bizServeInfoService.queryById(s);
            bizServeInfo.setEnable(true);
            bizServeInfoService.update(bizServeInfo);
        }
        return ResponseData.success("批量发布成功");
    }

    @Override
    public ResponseData plqxqy(IdsDto idsDto) {
        String ids = idsDto.getIds();
        String[] split = ids.split(",");
        for (String s : split) {
            BizServeInfo bizServeInfo = bizServeInfoService.queryById(s);
            bizServeInfo.setEnable(false);
            bizServeInfoService.update(bizServeInfo);
        }
        return ResponseData.success("批量取消发布成功");
    }

    @Override
    public ResponseData<Void> saveMethodDefault(MethodDefaultDTO dto) {
        JSONObject json = new JSONObject();
        json.put("method", dto.getMethod());
        json.put("iconId", dto.getIconId());
        json.put("description", dto.getDescription());
        List<Object> list = redisUtil.lGet(redisKey,  0, -1);
        List<Object> newList = Lists.newArrayList();
        if (list != null && !list.isEmpty()) {
            boolean found = false;
            for (Object o : list) {
                JSONObject jsonObject = JSONObject.parseObject(o.toString());
                if (jsonObject.getString("method").equals(dto.getMethod())) {
                    // 替换旧值
                    newList.add(json);
                    found = true;
                } else {
                    newList.add(o);
                }
            }
            // 如果未找到匹配项，则添加新元素
            if (!found) {
                newList.add(json);
            }
        } else {
            newList.add(json);
        }
        redisUtil.del(redisKey);
        redisUtil.lSetList(redisKey, newList);
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<List<MethodDefaultDTO>> queryMethodDefault(String method) {
        List<Object> list = redisUtil.lGet(redisKey, 0, -1);
        List<MethodDefaultDTO> dtoList = JSONArray.parseArray(list == null ? "[]" : JSONArray.toJSONString(list), MethodDefaultDTO.class);
        if (StringUtils.isNotBlank(method)){
            List<MethodDefaultDTO> dto = Lists.newArrayList();
            for (MethodDefaultDTO methodDefaultDTO : dtoList) {
                if (methodDefaultDTO.getMethod().equals(method)){
                    dto.add(methodDefaultDTO);
                    return ResponseData.success(dto);
                }
            }
        }
        return ResponseData.success(dtoList);
    }

    @Override
    public ResponseData<Void> deleteMethodDefault(String method) {
        List<Object> list = redisUtil.lGet(redisKey,  0, -1);
        List<Object> newList = Lists.newArrayList();
        if (list != null && !list.isEmpty()) {
            for (Object o : list) {
                JSONObject jsonObject = JSONObject.parseObject(o.toString());
                if (!jsonObject.getString("method").equals(method)) {
                    newList.add(o);
                }
            }
        }
        redisUtil.del(redisKey);
        if (!newList.isEmpty()){
            redisUtil.lSetList(redisKey, newList);
        }
        return ResponseData.success().build();
    }
}
