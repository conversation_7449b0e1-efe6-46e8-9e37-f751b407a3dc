package com.workplat.matter.api;

import cn.hutool.core.map.MapUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.workplat.accept.business.serve.entity.BizServeInfo;
import com.workplat.accept.business.serve.entity.BizServeMethod;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.service.item.dubbo.matter.entity.accept.ConfMatterAccept;
import com.workplat.gss.service.item.dubbo.matter.service.accept.ConfMatterAcceptService;
import com.workplat.matter.converter.BizNetworkWindowConverter;
import com.workplat.matter.entity.BizNetworkWindow;
import com.workplat.matter.service.BizNetworkWindowService;
import com.workplat.matter.vo.NetworkWindowDTO;
import com.workplat.matter.vo.NetworkWindowRequest;
import com.workplat.serve.service.BizServeInfoService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.api
 * @description
 * @date 2025/6/10 14:05
 */
@RestController
public class NetworkWindowApiImpl implements NetworkWindowApi{

    @Autowired
    private BizNetworkWindowService networkWindowService;
    @Autowired
    private BizNetworkWindowConverter networkWindowConverter;
    @Autowired
    private ConfMatterAcceptService matterAcceptService;
    @Autowired
    private BizServeInfoService serveInfoService;

    @Override
    public ResponseData<Void> save(NetworkWindowDTO dto) {
        BizNetworkWindow window = new BizNetworkWindow();
        if (StringUtils.isNotBlank(dto.getId())){
            BizNetworkWindow old = networkWindowService.queryById(dto.getId());
            if (old != null){
                window = old;
            }
        }
        window.setWindowInfo(dto.getWindowInfo());
        window.setLatitude(dto.getLatitude());
        window.setLongitude(dto.getLongitude());
        window.setNetworkName(dto.getNetworkName());
        window.setCode(dto.getCode());
        networkWindowService.save(window);
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<Void> deleteById(String id) {
        networkWindowService.deleteById(id);
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<NetworkWindowDTO> getById(String id) {
        BizNetworkWindow networkWindow = networkWindowService.queryById(id);
        if (networkWindow != null){
            return ResponseData.success(networkWindowConverter.convert(networkWindow));
        }
        return ResponseData.success(null);
    }

    @Override
    public ResponseData<Page<NetworkWindowDTO>> page(NetworkWindowRequest request, PageableDTO pageDTO) {
        Map<String, Object> params = MapUtil.<String, Object>builder().build();
        if (StringUtils.isNotBlank(request.getNetworkName())){
            params.put("like(networkName)", request.getNetworkName());
        }
        if (StringUtils.isNotBlank(request.getCode())){
            params.put("=(code)", request.getCode());
        }
        if (StringUtils.isNotBlank(request.getWindowInfo())){
            params.put("like(windowInfo)", request.getWindowInfo());
        }
        Page<BizNetworkWindow> page = networkWindowService.queryForPage(params, pageDTO.convertPageable());
        return ResponseData.success(networkWindowConverter.convert(page));
    }

    @Override
    public ResponseData<List<NetworkWindowDTO>> queryByIds(String ids) {
        String[] idList = ids.split(",");
        List<BizNetworkWindow> networkWindows = networkWindowService.queryByIds(idList);
        return ResponseData.success(networkWindowConverter.convert(networkWindows));
    }

    @Override
    public ResponseData<List<NetworkWindowDTO>> queryByAcceptId(String acceptId) {
        ConfMatterAccept matterAccept = matterAcceptService.queryForSingle(Map.of("=(id)", acceptId));
        List<NetworkWindowDTO> list = Lists.newArrayList();
        if (matterAccept != null){
            BizServeInfo serveInfo = serveInfoService.queryForSingle(Map.of("=(code)", matterAccept.getMatterId()));
            if (serveInfo != null){
                for (BizServeMethod method : serveInfo.getMethodList()){
                    if ("XXB".equals(method.getType())){
                        list = networkWindowConverter.convert(networkWindowService.queryByIds(method.getContent().split(",")));
                    }
                }
            }
        }
        return ResponseData.success(list);
    }
}
