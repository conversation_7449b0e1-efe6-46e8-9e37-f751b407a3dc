package com.workplat.matter.api;

import cn.hutool.core.map.MapUtil;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.matter.entity.BizAuthOperation;
import com.workplat.matter.entity.BizReadConfirm;
import com.workplat.matter.service.BizAuthOperationService;
import com.workplat.matter.service.BizReadConfirmService;
import com.workplat.matter.vo.AuthOperationDTO;
import com.workplat.matter.vo.AuthOperationVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.api
 * @description
 * @date 2025/6/6 14:19
 */
@RestController
public class AuthOperationApiImpl implements AuthOperationApi{

    @Autowired
    private BizAuthOperationService authOperationService;
    @Autowired
    private BizReadConfirmService readConfirmService;

    @Override
    public ResponseData<Void> createAuth(AuthOperationDTO dto) {
        BizAuthOperation operation = new BizAuthOperation();
        operation.setName(dto.getName());
        operation.setPhone(dto.getPhone());
        operation.setIdCard(dto.getIdCard());
        operation.setInstanceId(dto.getInstanceId());
        operation.setType(dto.getType());
        authOperationService.save(operation);
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<AuthOperationVO> checkAuth(AuthOperationDTO dto) {
        BizAuthOperation operation = authOperationService.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(idCard)", dto.getIdCard())
                .put("=(instanceId)", dto.getInstanceId())
                .put("=(type)", dto.getType())
                .build());
        if (operation != null){
            AuthOperationVO vo  = new AuthOperationVO();
            vo.setInstanceId(operation.getInstanceId());
            vo.setSignFileUrl(operation.getSignFileUrl());
            vo.setFace(operation.isFace());
            return ResponseData.success(vo);
        }
        return ResponseData.success(null);
    }

    @Override
    public ResponseData<Void> updateAuth(AuthOperationDTO dto) {
        BizAuthOperation operation = authOperationService.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(idCard)", dto.getIdCard())
                .put("=(instanceId)", dto.getInstanceId())
                .put("=(type)", dto.getType())
                .build());
        if (operation == null){
            return ResponseData.error("授权信息不存在");
        }
        switch (dto.getType()){
            case "sign":
                operation.setSignFileUrl(dto.getSignFileUrl());
                break;
            case "face":
                operation.setFace(dto.isFace());
                break;
        }
        authOperationService.save(operation);
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<Boolean> isRead(String userId, String readCode) {
        BizReadConfirm readConfirm = readConfirmService.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(userId)", userId)
                .put("=(readCode)", readCode)
                .build());
        if (readConfirm != null){
            return ResponseData.success(readConfirm.isRead());
        }
        return ResponseData.success(false);
    }

    @Override
    public ResponseData<Void> setRead(String userId, String readCode) {
        BizReadConfirm readConfirm = new BizReadConfirm();
        readConfirm.setUserId(userId);
        readConfirm.setReadCode(readCode);
        BizReadConfirm oldConfirm = readConfirmService.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(userId)", userId)
                .put("=(readCode)", readCode)
                .build());
        if (oldConfirm != null){
            readConfirm = oldConfirm;
        }
        readConfirm.setRead(true);
        readConfirmService.save(readConfirm);
        return ResponseData.success().build();
    }
}
