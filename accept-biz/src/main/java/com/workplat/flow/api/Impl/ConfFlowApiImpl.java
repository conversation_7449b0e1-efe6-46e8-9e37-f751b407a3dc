package com.workplat.flow.api.Impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.conf.component.entity.ConfComponent;
import com.workplat.conf.component.service.ConfComponentService;
import com.workplat.flow.ConfFlowApi;
import com.workplat.flow.converter.ConfFlowDTOConverter;
import com.workplat.flow.dto.ConfFlowDTO;
import com.workplat.flow.dto.ProgressPercentageDTO;
import com.workplat.flow.entity.ConfFlow;
import com.workplat.flow.entity.ConfFlowComponent;
import com.workplat.flow.service.ConfFlowComponentService;
import com.workplat.flow.service.ConfFlowService;
import com.workplat.gss.application.dubbo.entity.BizInstanceFields;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Objects;

/**
 * <AUTHOR>
 * @ClassName ConfFlowApiImpl
 * @Description
 * @Date 2025/5/22
 * @Version 1.0.0
 **/
@RestController
public class ConfFlowApiImpl implements ConfFlowApi {

    @Value("${dify.api:http://***************/v1}")
    private String url;
    @Value("${businessWorkFlowKey:app-Jxa8uKSI1A5EVGi0ZyYlbNmq}")
    private String businessWorkFlowKey;

    @Autowired
    private ConfFlowService confFlowService;

    @Autowired
    private ConfFlowComponentService confFlowComponentService;

    @Autowired
    private ConfComponentService confComponentService;

    @Autowired
    private ConfFlowDTOConverter confFlowDTOConverter;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private BizInstanceFieldsService instanceFieldsService;

    @Override
    public ResponseData<Void> save(ConfFlowDTO confFlowDTO) {

        ConfFlow confFlow = new ConfFlow();
        // 编辑操作
        if (StringUtils.isNotBlank(confFlowDTO.getId())){
            confFlow = confFlowService.queryById(confFlowDTO.getId());
            // 删除流程的原组件
            confFlowComponentService.deleteByParams(MapUtil.<String, Object>builder().put("=(confFlow.id)", confFlowDTO.getId()).build());
            // 重复编码判断
        } else if (confFlowService.queryForCount(MapUtil.<String, Object>builder().put("=(code)", confFlowDTO.getCode()).build()) > 0){
            return ResponseData.error("流程编码已存在");
        }
        if (StringUtils.isNotBlank(confFlowDTO.getName())){
            confFlow.setName(confFlowDTO.getName());
        }
        if (StringUtils.isNotBlank(confFlowDTO.getCode())){
            confFlow.setCode(confFlowDTO.getCode());
        }
        if (StringUtils.isNotBlank(confFlowDTO.getDescription())){
            confFlow.setDescription(confFlowDTO.getDescription());
        }
        confFlowService.save(confFlow);

        for (ConfFlowDTO.ConfComponentDTO confComponentDTO : confFlowDTO.getComponents()) {
            ConfComponent confComponent = confComponentService.queryById(confComponentDTO.getComponentId());
            ConfFlowComponent confFlowComponent = new ConfFlowComponent();
            confFlowComponent.setConfComponent(confComponent);
            confFlowComponent.setConfFlow(confFlow);
            confFlowComponent.setConfiguration(confComponentDTO.getConfiguration());
            confFlowComponent.setSequenceOrder(confComponentDTO.getSequenceOrder());
            confFlowComponentService.save(confFlowComponent);
        }
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<Page<ConfFlowDTO>> page(PageableDTO pageDTO, String keyword) {
        HashMap<String, Object> param = new HashMap<>();
        if (StringUtil.isNotBlank(keyword)) {
            param.put("like(name)", keyword);
        }
        Page<ConfFlow> page = confFlowService.queryForPage(param, pageDTO.convertPageable());
        return ResponseData.success(confFlowDTOConverter.convert(page));
    }

    @Override
    public ResponseData<ConfFlowDTO> queryById(String id) {
        ConfFlow confFlow = confFlowService.queryById(id);
        if (confFlow != null){
            return ResponseData.success(confFlowDTOConverter.convert(confFlow));
        }
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<ConfFlowDTO> queryByCode(String code) {
        ConfFlow confFlow = confFlowService.queryForSingle(MapUtil.<String, Object>builder().put("=(code)", code).build());
        if (confFlow != null){
            return ResponseData.success(confFlowDTOConverter.convert(confFlow));
        }
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<Void> deleteById(String id) {
        confFlowService.deleteById(id);
        confFlowComponentService.deleteByParams(MapUtil.<String, Object>builder().put("=(confFlow.id)", id).build());
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<String> getProgressPercentage(ProgressPercentageDTO dto) {
        String getConversationsUrl = url + "/conversations/" + dto.getChatId() + "/variables?user=" + dto.getUserId();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(businessWorkFlowKey);
        HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);
        ResponseEntity<String> strResult = restTemplate.exchange(getConversationsUrl, HttpMethod.GET, requestEntity, String.class);
        JSONArray array = Objects.requireNonNull(JSON.parseObject(strResult.getBody())).getJSONArray("data");
        // <当前流程节点>环境变量
        String currentFlow = "";
        // <流程节点>环境变量
        String flowNode = "";
        for (int i = 0; i < array.size(); i++) {
            JSONObject jsonObject = array.getJSONObject(i);
            if ("currentFlow".equals(jsonObject.getString("name"))){
                currentFlow = jsonObject.getString("value");
            }
            if ("flowNode".equals(jsonObject.getString("name"))){
                flowNode = jsonObject.getString("value");
            }
        }
        String[] nodesArray = flowNode.replaceAll("[\\[\\]' ]", "").split(",");
        // 查找当前节点的索引位置
        int currentIndex = -1;
        for (int i = 0; i < nodesArray.length; i++) {
            if (currentFlow.equals(nodesArray[i])) {
                currentIndex = i;
                break;
            }
        }
        // 如果没找到当前节点，返回0
        if (currentIndex == -1) {
            return ResponseData.success("0%");
        }
        BizInstanceFields fields = instanceFieldsService.queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", dto.getInstanceId()).build());
        int  percentage = (currentIndex + 1) * 50 / nodesArray.length;
        // 已经到最后一个流程节点，返回100%
        if (percentage == 50){
            return ResponseData.success("100%");
        }
        //  没有字段信息，只展示流程百分比，流程权重50%，字段权重50%
        if (fields != null && !StringUtils.isBlank(fields.getFormObj())) {
            int totalFields;
            int completedFields = JSON.parseObject(fields.getFormObj()).size();
            if (StringUtils.isNotBlank(fields.getFieldsFilterMap()) && !JSON.parseObject(fields.getFieldsFilterMap()).isEmpty()) {
                totalFields = JSON.parseObject(fields.getFieldsFilterMap()).size();
            } else {
                totalFields = JSON.parseObject(fields.getFieldsMap()).size();
            }
            percentage = completedFields * 50 / totalFields + percentage;
        }
        return ResponseData.success(percentage + "%");
    }
}
