package com.workplat.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.google.common.collect.ImmutableMap;

import java.util.Iterator;
import java.util.Map;

public class FormJsonProcessor {

    /**
     * 处理表单JSON数据，只保留fieldMap中包含的字段，删除其他字段
     * 同时保留和更新相关的配置信息
     *
     * @param formJson 原始表单JSON字符串
     * @param fieldMap 字段值映射
     * @return 处理后的JSON对象
     */
    public static JSONObject processFormJson(String formJson, Map<String, Object> fieldMap) {
        // 将JSON字符串转换为JSONObject
        JSONObject jsonObject = JSON.parseObject(formJson);
        
        // 获取renderList数组
        JSONArray renderList = jsonObject.getJSONArray("renderList");
        
        // 使用迭代器遍历，以便能够安全地删除元素
        Iterator<Object> iterator = renderList.iterator();
        while (iterator.hasNext()) {
            JSONObject formArea = (JSONObject) iterator.next();
            JSONArray children = formArea.getJSONArray("child");
            
            // 处理子元素，返回是否有匹配的字段
            boolean hasMatchedFields = processChildren(children, fieldMap);
            
            // 如果没有匹配的字段，删除整个表单域
            if (!hasMatchedFields) {
                iterator.remove();
            }
        }
        
        return jsonObject;
    }
    
    /**
     * 递归处理子元素
     *
     * @param children 子元素数组
     * @param fieldMap 字段值映射
     * @return 是否有匹配的字段
     */
    private static boolean processChildren(JSONArray children, Map<String, Object> fieldMap) {
        if (children == null || children.isEmpty()) {
            return false;
        }
        
        boolean hasMatchedFields = false;
        Iterator<Object> iterator = children.iterator();
        
        while (iterator.hasNext()) {
            JSONObject child = (JSONObject) iterator.next();
            boolean shouldKeep = false;
            
            // 如果有子元素，递归处理
            if (child.containsKey("child")) {
                JSONArray subChildren = child.getJSONArray("child");
                if (processChildren(subChildren, fieldMap)) {
                    shouldKeep = true;
                }
            }
            
            JSONObject props = child.getJSONObject("props");
            if (props != null) {
                String field = props.getString("field");
                
                // 检查当前字段是否在fieldMap中
                if (field != null && fieldMap.containsKey(field)) {
                    // 更新字段值
                    Object value = fieldMap.get(field);
                    props.put("modelValue", value);
                    
                    // 处理特殊组件的值
                    handleSpecialComponentValue(child, value);
                    
                    shouldKeep = true;
                    hasMatchedFields = true;
                }
            }
            
            // 如果当前节点和其子节点都不包含匹配的字段，则删除该节点
            if (!shouldKeep) {
                iterator.remove();
            }
        }
        
        return hasMatchedFields;
    }
    
    /**
     * 处理特殊组件的值
     *
     * @param child 组件对象
     * @param value 字段值
     */
    private static void handleSpecialComponentValue(JSONObject child, Object value) {
        String componentName = child.getString("componentName");
        if (componentName == null) {
            return;
        }

        JSONObject props = child.getJSONObject("props");
        
        switch (componentName.toLowerCase()) {
            case "a-net-radio":
                // 处理单选框的值
                handleRadioValue(props, value);
                break;
            case "a-net-select":
                // 处理下拉框的值
                handleSelectValue(props, value);
                break;
            // 可以添加其他特殊组件的处理
            default:
                // 默认不做特殊处理
                break;
        }
    }

    /**
     * 处理单选框的值
     */
    private static void handleRadioValue(JSONObject props, Object value) {
        if (value == null) {
            return;
        }
        
        JSONArray options = props.getJSONArray("options");
        if (options != null) {
            for (int i = 0; i < options.size(); i++) {
                JSONObject option = options.getJSONObject(i);
                if (value.toString().equals(option.getString("value"))) {
                    option.put("checked", true);
                } else {
                    option.put("checked", false);
                }
            }
        }
    }

    /**
     * 处理下拉框的值
     */
    private static void handleSelectValue(JSONObject props, Object value) {
        if (value == null) {
            return;
        }
        
        JSONArray options = props.getJSONArray("options");
        if (options != null) {
            for (int i = 0; i < options.size(); i++) {
                JSONObject option = options.getJSONObject(i);
                if (value.toString().equals(option.getString("value"))) {
                    option.put("selected", true);
                } else {
                    option.put("selected", false);
                }
            }
        }
    }

    public static void main(String[] args) {
        String formjson = "{\n" +
                "  \"tableName\": \"\",\n" +
                "  \"renderList\": [\n" +
                "    {\n" +
                "      \"id\": \"3N1EzOVzND\",\n" +
                "      \"icon\": \"icon-biaodan\",\n" +
                "      \"name\": \"可新增表格\",\n" +
                "      \"type\": \"addTable\",\n" +
                "      \"child\": [\n" +
                "        {\n" +
                "          \"child\": [\n" +
                "            {\n" +
                "              \"id\": \"6Ll7WEsbu\",\n" +
                "              \"icon\": \"icon-select\",\n" +
                "              \"name\": \"下拉框\",\n" +
                "              \"child\": [],\n" +
                "              \"props\": {\n" +
                "                \"id\": \"8794900ef8494272a5e32bfaeb24615c\",\n" +
                "                \"key\": \"sslx\",\n" +
                "                \"field\": \"sslx\",\n" +
                "                \"label\": \"设施类型\",\n" +
                "                \"default\": \"\",\n" +
                "                \"options\": [\n" +
                "                  {\n" +
                "                    \"label\": \"自行车库\",\n" +
                "                    \"value\": \"sslx1\",\n" +
                "                    \"disabled\": false\n" +
                "                  },\n" +
                "                  {\n" +
                "                    \"label\": \"汽车库\",\n" +
                "                    \"value\": \"sslx2\",\n" +
                "                    \"disabled\": false\n" +
                "                  }\n" +
                "                ],\n" +
                "                \"disabled\": false,\n" +
                "                \"modelValue\": \"\"\n" +
                "              },\n" +
                "              \"componentName\": \"a-net-select\"\n" +
                "            },\n" +
                "            {\n" +
                "              \"id\": \"W2w00LEge\",\n" +
                "              \"icon\": \"icon-input\",\n" +
                "              \"name\": \"输入框\",\n" +
                "              \"child\": [],\n" +
                "              \"props\": {\n" +
                "                \"id\": \"419b5c0f6eae42239126565a3a3f4c67\",\n" +
                "                \"key\": \"ssmj\",\n" +
                "                \"field\": \"ssmj\",\n" +
                "                \"label\": \"设施面积(㎡)\",\n" +
                "                \"default\": \"\",\n" +
                "                \"modelValue\": \"\"\n" +
                "              },\n" +
                "              \"componentName\": \"a-net-input\"\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      ],\n" +
                "      \"props\": {\n" +
                "        \"key\": \"\",\n" +
                "        \"title\": \"附属设施\"\n" +
                "      },\n" +
                "      \"componentName\": \"ANetCanAddTable\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"formAttribute\": {\n" +
                "    \"inline\": false,\n" +
                "    \"disabled\": false,\n" +
                "    \"tableName\": \"demoFormName\",\n" +
                "    \"labelWidth\": \"100px\",\n" +
                "    \"labelPosition\": \"right\"\n" +
                "  },\n" +
                "  \"beforeFunction\": \"{}\",\n" +
                "  \"submitFunction\": \"{}\"\n" +
                "}";

        // 测试场景1：只更新ssmj字段
        System.out.println("测试场景1：只更新ssmj字段");
        JSONObject result1 = processFormJson(formjson, ImmutableMap.of("ssmj", "123"));
        System.out.println(JSONObject.toJSONString(result1, JSONWriter.Feature.PrettyFormat));

        // 测试场景2：更新sslx字段
        System.out.println("\n测试场景2：只更新sslx字段");
        JSONObject result2 = processFormJson(formjson, ImmutableMap.of("sslx", "sslx1"));
        System.out.println(JSONObject.toJSONString(result2, JSONWriter.Feature.PrettyFormat));

        // 测试场景3：同时更新两个字段
        System.out.println("\n测试场景3：同时更新两个字段");
        JSONObject result3 = processFormJson(formjson, ImmutableMap.of("ssmj", "123", "sslx", "sslx2"));
        System.out.println(JSONObject.toJSONString(result3, JSONWriter.Feature.PrettyFormat));
    }
} 