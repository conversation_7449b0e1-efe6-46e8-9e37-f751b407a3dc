package com.workplat.utils;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;

import java.util.HashMap;
import java.util.Map;

public class FormStepProcessor {

    /**
     * 处理 JSON 表单 获取表单域的数量
     *
     * @param formJson 原始表单 JSON 字符串
     * @return 表单域的数量
     */
    public static int getFormStepCount(String formJson) {
        JSONObject result = new JSONObject();
        JSONObject formJsonObj = JSON.parseObject(formJson);

        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");
            return renderList.size();
        }
        return 0;
    }

    /**
     * 处理 JSON 表单并根据步骤编号返回特定的 renderList 项
     *
     * @param formJson 原始表单 JSON 字符串
     * @param step     步数 （0， 1， 2）
     * @return JSONObject，其中包含请求的步骤数据或空对象（如果步骤无效）
     */
    public static JSONObject getStepData(String formJson, int step) {
        // 步长减一，因为数组索引从0开始
        int stepIndex = step - 1;
        JSONObject result = new JSONObject();
        JSONObject formJsonObj = JSON.parseObject(formJson);

        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");

            if (stepIndex >= 0 && stepIndex < renderList.size()) {
                // Get the specific form area for the step
                JSONObject formArea = renderList.getJSONObject(stepIndex);

                // Create a new result object with the same structure but only the selected step
                result.put("tableName", formJsonObj.getString("tableName"));
                result.put("formAttribute", formJsonObj.getJSONObject("formAttribute"));
                result.put("beforeFunction", formJsonObj.getString("beforeFunction"));
                result.put("submitFunction", formJsonObj.getString("submitFunction"));

                // Create a new renderList with just the selected form area
                JSONArray singleStepRenderList = new JSONArray();
                singleStepRenderList.add(formArea);
                result.put("renderList", singleStepRenderList);
            }
        }

        return result;
    }

    /**
     * 返回指定步长之前的所有步长的替代版本
     *
     * @param formJson 原始表单 JSON 字符串
     * @param step     要包含的最大步数 （0， 1， 2）
     * @return JSONObject 包含指定步骤之前的所有步骤
     */
    public static JSONObject getStepsUpTo(String formJson, int step) {
        JSONObject result = new JSONObject();
        JSONObject formJsonObj = JSON.parseObject(formJson);

        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");
            JSONArray filteredRenderList = new JSONArray();

            // Add all steps up to the specified one
            for (int i = 0; i <= step && i < renderList.size(); i++) {
                filteredRenderList.add(renderList.getJSONObject(i));
            }

            // Build the result object
            result.put("tableName", formJsonObj.getString("tableName"));
            result.put("formAttribute", formJsonObj.getJSONObject("formAttribute"));
            result.put("beforeFunction", formJsonObj.getString("beforeFunction"));
            result.put("submitFunction", formJsonObj.getString("submitFunction"));
            result.put("renderList", filteredRenderList);
        }

        return result;
    }

    /**
     * 处理表单JSON，根据字段映射保留需要展示的表单项
     *
     * @param formJson 原始表单JSON字符串
     * @param fieldMap 字段值映射表
     * @return 处理后的表单JSON对象
     */
    public static JSONObject processFormJson(String formJson, Map<String, Object> fieldMap) {
        // 解析原始表单JSON
        JSONObject formJsonObj = JSON.parseObject(formJson);

        // 检查是否存在渲染列表
        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");
            JSONArray filteredRenderList = new JSONArray();

            // 遍历每个表单区域
            for (int i = 0; i < renderList.size(); i++) {
                JSONObject formArea = renderList.getJSONObject(i);

                // 检查表单区域是否包含子元素
                if (formArea.containsKey("child")) {
                    JSONArray children = formArea.getJSONArray("child");
                    JSONArray filteredChildren = new JSONArray();

                    // 过滤子组件
                    for (int j = 0; j < children.size(); j++) {
                        JSONObject child = children.getJSONObject(j);

                        // 检查子组件是否包含属性
                        if (child.containsKey("props")) {
                            JSONObject props = child.getJSONObject("props");
                            String field = props.getString("field");

                            // 如果字段存在于字段映射中，则保留该组件
                            if (field != null && fieldMap.containsKey(field)) {
                                filteredChildren.add(child);
                            }
                        }
                    }

                    // 只保留有匹配子组件的表单区域
                    if (!filteredChildren.isEmpty()) {
                        formArea.put("child", filteredChildren);
                        filteredRenderList.add(formArea);
                    }
                }
            }

            // 替换原始渲染列表为过滤后的列表
            formJsonObj.put("renderList", filteredRenderList);
        }

        return formJsonObj;
    }
}