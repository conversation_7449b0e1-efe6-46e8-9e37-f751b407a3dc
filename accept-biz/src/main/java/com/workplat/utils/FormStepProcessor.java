package com.workplat.utils;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;

import java.util.HashMap;
import java.util.Map;

public class FormStepProcessor {

    /**
     * 处理 JSON 表单 获取表单域的数量
     *
     * @param formJson 原始表单 JSON 字符串
     * @return 表单域的数量
     */
    public static int getFormStepCount(String formJson) {
        JSONObject result = new JSONObject();
        JSONObject formJsonObj = JSON.parseObject(formJson);

        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");
            return renderList.size();
        }
        return 0;
    }

    /**
     * 处理 JSON 表单并根据步骤编号返回特定的 renderList 项
     *
     * @param formJson 原始表单 JSON 字符串
     * @param step     步数 （0， 1， 2）
     * @return JSONObject，其中包含请求的步骤数据或空对象（如果步骤无效）
     */
    public static JSONObject getStepData(String formJson, int step) {
        // 步长减一，因为数组索引从0开始
        int stepIndex = step - 1;
        JSONObject result = new JSONObject();
        JSONObject formJsonObj = JSON.parseObject(formJson);

        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");

            if (stepIndex >= 0 && stepIndex < renderList.size()) {
                // Get the specific form area for the step
                JSONObject formArea = renderList.getJSONObject(stepIndex);

                // Create a new result object with the same structure but only the selected step
                result.put("tableName", formJsonObj.getString("tableName"));
                result.put("formAttribute", formJsonObj.getJSONObject("formAttribute"));
                result.put("beforeFunction", formJsonObj.getString("beforeFunction"));
                result.put("submitFunction", formJsonObj.getString("submitFunction"));

                // Create a new renderList with just the selected form area
                JSONArray singleStepRenderList = new JSONArray();
                singleStepRenderList.add(formArea);
                result.put("renderList", singleStepRenderList);
            }
        }

        return result;
    }

    /**
     * 返回指定步长之前的所有步长的替代版本
     *
     * @param formJson 原始表单 JSON 字符串
     * @param step     要包含的最大步数 （0， 1， 2）
     * @return JSONObject 包含指定步骤之前的所有步骤
     */
    public static JSONObject getStepsUpTo(String formJson, int step) {
        JSONObject result = new JSONObject();
        JSONObject formJsonObj = JSON.parseObject(formJson);

        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");
            JSONArray filteredRenderList = new JSONArray();

            // Add all steps up to the specified one
            for (int i = 0; i <= step && i < renderList.size(); i++) {
                filteredRenderList.add(renderList.getJSONObject(i));
            }

            // Build the result object
            result.put("tableName", formJsonObj.getString("tableName"));
            result.put("formAttribute", formJsonObj.getJSONObject("formAttribute"));
            result.put("beforeFunction", formJsonObj.getString("beforeFunction"));
            result.put("submitFunction", formJsonObj.getString("submitFunction"));
            result.put("renderList", filteredRenderList);
        }

        return result;
    }

    /**
     * 处理表单JSON，根据字段映射保留需要展示的表单项
     *
     * @param formJson 原始表单JSON字符串
     * @param fieldMap 字段值映射表
     * @return 处理后的表单JSON对象
     */
    public static JSONObject processFormJson(String formJson, Map<String, Object> fieldMap) {
        // 解析原始表单JSON
        JSONObject formJsonObj = JSON.parseObject(formJson);

        // 检查是否存在渲染列表
        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");
            JSONArray filteredRenderList = new JSONArray();

            // 遍历每个表单区域
            for (int i = 0; i < renderList.size(); i++) {
                JSONObject formArea = renderList.getJSONObject(i);

                // 递归处理子组件
                if (formArea.containsKey("child")) {
                    JSONArray children = formArea.getJSONArray("child");
                    JSONArray filteredChildren = processChildrenRecursively(children, fieldMap);

                    // 只保留有匹配子组件的表单区域
                    if (!filteredChildren.isEmpty()) {
                        formArea.put("child", filteredChildren);
                        filteredRenderList.add(formArea);
                    }
                }
            }

            // 替换原始渲染列表为过滤后的列表
            formJsonObj.put("renderList", filteredRenderList);
        }

        return formJsonObj;
    }

    /**
     * 递归处理子组件，支持多层嵌套结构
     *
     * @param children 子组件数组
     * @param fieldMap 字段值映射表
     * @return 过滤后的子组件数组
     */
    private static JSONArray processChildrenRecursively(JSONArray children, Map<String, Object> fieldMap) {
        JSONArray filteredChildren = new JSONArray();

        for (int i = 0; i < children.size(); i++) {
            JSONObject child = children.getJSONObject(i);
            boolean shouldKeepChild = false;

            // 检查当前组件是否有field属性且在fieldMap中
            if (child.containsKey("props")) {
                JSONObject props = child.getJSONObject("props");
                String field = props.getString("field");

                if (field != null && fieldMap.containsKey(field)) {
                    // 更新字段值
                    Object value = fieldMap.get(field);
                    props.put("modelValue", value);
                    shouldKeepChild = true;
                }
            }

            // 递归处理子组件的子元素
            if (child.containsKey("child")) {
                JSONArray subChildren = child.getJSONArray("child");
                JSONArray filteredSubChildren = processChildrenRecursively(subChildren, fieldMap);

                if (!filteredSubChildren.isEmpty()) {
                    child.put("child", filteredSubChildren);
                    shouldKeepChild = true;
                }
            }

            // 如果当前组件或其子组件有匹配的字段，则保留
            if (shouldKeepChild) {
                filteredChildren.add(child);
            }
        }

        return filteredChildren;
    }

    /**
     * 测试方法 - 验证新的表单结构处理
     */
    public static void main(String[] args) {
        // 读取新的表单JSON文件进行测试
        try {
            String formJson = java.nio.file.Files.readString(
                java.nio.file.Paths.get("accept-biz/src/main/java/com/workplat/utils/form.json"));

            // 测试场景1：只保留sslx字段（嵌套在可新增表格中）
            System.out.println("=== 测试场景1：只保留sslx字段 ===");
            Map<String, Object> fieldMap1 = new HashMap<>();
            fieldMap1.put("sslx", "sslx1");
            JSONObject result1 = processFormJson(formJson, fieldMap1);
            System.out.println("结果：" + result1.toJSONString());

            // 测试场景2：只保留cqsyfs字段（在表单域中）
            System.out.println("\n=== 测试场景2：只保留cqsyfs字段 ===");
            Map<String, Object> fieldMap2 = new HashMap<>();
            fieldMap2.put("cqsyfs", "cqsyfs1");
            JSONObject result2 = processFormJson(formJson, fieldMap2);
            System.out.println("结果：" + result2.toJSONString());

            // 测试场景3：保留多个字段
            System.out.println("\n=== 测试场景3：保留多个字段 ===");
            Map<String, Object> fieldMap3 = new HashMap<>();
            fieldMap3.put("sslx", "sslx2");
            fieldMap3.put("ssmj", "100");
            fieldMap3.put("cqsyfs", "cqsyfs2");
            JSONObject result3 = processFormJson(formJson, fieldMap3);
            System.out.println("结果：" + result3.toJSONString());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}