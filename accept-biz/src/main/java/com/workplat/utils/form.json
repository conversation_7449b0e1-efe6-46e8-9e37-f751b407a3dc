{"tableName": "", "renderList": [{"id": "3N1EzOVzND", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "可新增表格", "type": "addTable", "child": [{"child": [{"id": "6Ll7WEsbu", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"id": "8794900ef8494272a5e32bfaeb24615c", "key": "sslx", "field": "sslx", "label": "设施类型", "default": "", "options": [{"label": "自行车库", "value": "sslx1", "disabled": false}, {"label": "汽车库", "value": "sslx2", "disabled": false}, {"label": "阁楼", "value": "sslx3", "disabled": false}, {"label": "储藏室", "value": "sslx4", "disabled": false}, {"label": "地下室", "value": "sslx5", "disabled": false}], "disabled": false, "functions": [{"key": "changesslx", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择"}, "rules": [{"message": "设施类型不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "W2w00LEge", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "419b5c0f6eae42239126565a3a3f4c67", "key": "ssmj", "field": "ssmj", "label": "设施面积(㎡)", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "<PERSON>smj", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsssmj", "name": "foucs事件", "value": ""}, {"key": "<PERSON>smj", "name": "change事件", "value": ""}, {"key": "clickssmj", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "showAppendBtn": false, "appendBtnColor": "#2c8ef1"}, "rules": [{"message": "设施面积不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}]}], "props": {"key": "", "max": 1, "min": 1, "field": "8-jXaTuTV", "title": "附属设施", "addText": "新增", "functions": [{"key": "delete8-jXaTuTV", "name": "delete事件", "value": ""}, {"key": "add8-jXaTuTV", "name": "add事件", "value": ""}], "isNeedMax": false, "isNeedMin": false, "deleteText": "删除", "innerWidth": 1, "outerWidth": 1, "defaultLine": 0, "isSelection": false, "isShowIndex": true, "showColumns": "", "isAddByDialog": false, "isShowAsTable": false, "isShowOutBorder": true, "innerBorderColor": "#000", "outerBorderColor": "#000", "isShowInnerBorder": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetCanAddTable"}, {"id": "U-bIv122c4", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "表单域", "type": "formArea", "child": [{"id": "8BSfr4RyS", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"id": "92fd6f8703a94116bb585ac02ed225ba", "key": "cqsyfs", "field": "cqsyfs", "label": "产权所有方式", "default": "", "options": [{"label": "单独所有", "value": "cqsyfs1", "disabled": false}, {"label": "共同共有", "value": "cqsyfs2", "disabled": false}, {"label": "按份共有", "value": "cqsyfs3", "disabled": false}], "disabled": false, "functions": [{"key": "changecqsyfs", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": "若有抵押，登簿信息应与贷款合同中的抵押人一致。"}, "rules": [{"message": "产权所有方式不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "l1MTVkzhE", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "dd86eec617fb476d975d69ea710f2217", "key": "cqsyqfsbzsm", "field": "cqsyqfsbzsm", "label": "产权所有权方式备注说明", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputcqsyqfsbzsm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucscqsyqfsbzsm", "name": "foucs事件", "value": ""}, {"key": "changecqsyqfsbzsm", "name": "change事件", "value": ""}, {"key": "clickcqsyqfsbzsm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "产权所有权方式备注说明不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}], "props": {"show": true, "field": "d34aVgq4m", "title": "不动产登簿信息", "bgColor": "#dd4b39", "barColor": "", "isHidden": false, "functions": [], "titleSize": 22, "arrowColor": "#000000", "titleColor": "#0E0D0D", "isShowButton": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetFormArea"}], "formAttribute": {"inline": false, "disabled": false, "tableName": "demoFormName", "labelWidth": "100px", "labelPosition": "right"}, "beforeFunction": "{}", "submitFunction": "{}"}