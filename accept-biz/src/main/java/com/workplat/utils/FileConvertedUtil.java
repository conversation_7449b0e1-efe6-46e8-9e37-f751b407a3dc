package com.workplat.utils;

import org.apache.commons.io.FileUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class FileConvertedUtil {

    /**
     * 将PDF文件转换为单张图片
     *
     * @param pdfFile PDF文件
     * @return 转换后的图片文件列表
     * @throws IOException 文件处理异常
     */
    public static byte[] convertPdfToSingleImage(File pdfFile, String imageFormat) throws IOException {
        try (PDDocument document = PDDocument.load(pdfFile)) {
            PDFRenderer renderer = new PDFRenderer(document);

            // 计算总高度：累加所有页面的高度
            int totalHeight = 0;
            int pageWidth = 0;
            List<BufferedImage> images = new ArrayList<>();
            for (int i = 0; i < document.getNumberOfPages(); i++) {
                BufferedImage image = renderer.renderImageWithDPI(i, 300); // 300 DPI
                images.add(image);
                if (i == 0) {
                    pageWidth = image.getWidth();
                }
                totalHeight += image.getHeight();
            }

            // 创建一个大图，宽度为PDF页宽，高度为所有页面高度之和
            BufferedImage combinedImage = new BufferedImage(pageWidth, totalHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = combinedImage.createGraphics();

            int currentHeight = 0;
            for (BufferedImage image : images) {
                g2d.drawImage(image, 0, currentHeight, null);
                currentHeight += image.getHeight();
            }

            g2d.dispose();

            // 转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(combinedImage, imageFormat, baos); // 使用传入的图片格式
            return baos.toByteArray();
        }
    }


    public static void main(String[] args) {

        try {
            // 调用
            byte[] result = convertPdfToSingleImage(new File("C:\\Users\\<USER>\\Downloads\\劳务派遣经营许可申请书-新办（文书模板）.pdf"), "JPEG");
            // 存储为文件 pdf
            FileUtils.writeByteArrayToFile(new File("C:\\Users\\<USER>\\Desktop\\test.jpeg"), result);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }
}
