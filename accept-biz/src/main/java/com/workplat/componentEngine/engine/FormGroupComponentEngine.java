package com.workplat.componentEngine.engine;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.accept.business.chat.vo.StreamMessageVO;
import com.workplat.componentEngine.engine.content.InstructionConstant;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.entity.BizInstanceFields;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.utils.ChatCacheUtil;
import com.workplat.utils.FormStepProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 表单分组组件
 *
 * <AUTHOR>
 * @date 2025/06/04
 */
@Slf4j
@Service
public class FormGroupComponentEngine extends AbstractComponentEngine {

    private final BizInstanceFieldsService instanceFieldsService;
    private final ChatCacheUtil chatCacheUtil;


    public FormGroupComponentEngine(BizInstanceFieldsService instanceFieldsService, ChatCacheUtil chatCacheUtil) {
        this.instanceFieldsService = instanceFieldsService;
        this.chatCacheUtil = chatCacheUtil;
    }

    public static final String CODE = "FormFill";

    @Override
    protected ComponentRunVO doExecute() {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
        // 获取表单信息
        BizInstanceFieldsVO bizInstanceFieldsVO =
                instanceFieldsService.queryByInstanceId(componentDataContext.getInstanceId(), true);
        //  获取表单分组总数
        int formStepCount = FormStepProcessor.getFormStepCount(bizInstanceFieldsVO.getFormJson());
        // 存储表单分组总数
        chatProcessDTO.setFormStepCount(formStepCount);
        // 存储表单分组当前进度
        int formStepIndex = chatProcessDTO.getFormStepIndex() == null ? 1 : chatProcessDTO.getFormStepIndex();
        chatProcessDTO.setFormStepIndex(formStepIndex);
        // 更新缓存
        chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);
        //  获取表单分组数据
        JSONObject formStepData = FormStepProcessor.getStepData(bizInstanceFieldsVO.getFormJson(), formStepIndex);
        //  设置表单数据
        bizInstanceFieldsVO.setFormJson(formStepData.toJSONString());
        //  输出结果
        ComponentRunVO.RenderData fieldFormRenderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(bizInstanceFieldsVO)
                .build();
        // 组装结果
        ComponentRunVO vo = new ComponentRunVO();
        List<ComponentRunVO.RenderData> renderDataList =
                Collections.singletonList(fieldFormRenderData);
        vo.setRenderData(renderDataList);
        vo.setTips("表单展示组件");
        return vo;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        // 填充数据
        log.info("表单分组组件填充数据");
        Object submitData = componentDataContext.getSubmitData();
        log.info("submitData:{}", JSON.toJSONString(submitData));
        // 更新填报的数据
        @SuppressWarnings("unchecked")
        Map<String, Object> submitDataMap = JSON.parseObject(submitData.toString(), Map.class);
        if (submitDataMap != null) {
            BizInstanceFieldsVO bizInstanceFieldsVO =
                    instanceFieldsService.queryByInstanceId(componentDataContext.getInstanceId(), true);
            String formObj = bizInstanceFieldsVO.getFormObj();
            if (formObj != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> formObjMap = JSON.parseObject(formObj, Map.class);
                formObjMap.putAll(submitDataMap);
                bizInstanceFieldsVO.setFormObj(JSON.toJSONString(formObjMap));
            } else {
                bizInstanceFieldsVO.setFormObj(JSON.toJSONString(submitDataMap));
            }
            BizInstanceFields bizInstanceFields = instanceFieldsService.
                    queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", componentDataContext.getInstanceId()).build());
            bizInstanceFields.setFormObj(bizInstanceFieldsVO.getFormObj());
            instanceFieldsService.update(bizInstanceFields);
        }
        // 对表单域进行数量增加
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
        chatProcessDTO.setFormStepIndex(chatProcessDTO.getFormStepIndex() + 1);
        chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);
        log.info("表单分组组件填充数据结束, 步骤{}", chatProcessDTO.getFormStepIndex());
    }

    @Override
    public String getNextInstruction(ComponentDataContext componentDataContext) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
        if (chatProcessDTO != null
                && chatProcessDTO.getFormStepCount() != null
                && chatProcessDTO.getFormStepCount() - 1 >= chatProcessDTO.getFormStepIndex()) {
            return InstructionConstant.KEEP_AT_PRESENT.getCode();

        }
        return super.getNextInstruction(componentDataContext);
    }
}
