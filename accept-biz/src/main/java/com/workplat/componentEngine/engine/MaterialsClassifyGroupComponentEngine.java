package com.workplat.componentEngine.engine;

import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.service.MaterialClassificationService;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.content.InstructionConstant;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialGroupVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.utils.ChatCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 材料分类分组组件引擎
 * 处理分组材料的分类，主要功能包括：
 * 1. 材料分组管理
 * 2. 分组材料的分类处理
 * 3. 分组进度跟踪
 *
 * <AUTHOR>
 * @date 2025-06-18 14:37
 */
@Slf4j
@Service
public class MaterialsClassifyGroupComponentEngine extends AbstractMaterialClassifyEngine {

    /** 业务实例材料服务 */
    private final BizInstanceMaterialService bizInstanceMaterialService;

    /**
     * 构造函数
     *
     * @param materialClassificationService 材料分类服务
     * @param bizInstanceMaterialService 业务实例材料服务
     * @param instanceFieldsService 实例字段服务
     * @param chatCacheUtil 聊天缓存工具
     */
    public MaterialsClassifyGroupComponentEngine(MaterialClassificationService materialClassificationService,
                                              BizInstanceMaterialService bizInstanceMaterialService,
                                              BizInstanceFieldsService instanceFieldsService,
                                              ChatCacheUtil chatCacheUtil) {
        super(materialClassificationService, chatCacheUtil, instanceFieldsService,bizInstanceMaterialService);
        this.bizInstanceMaterialService = bizInstanceMaterialService;
    }

    /**
     * 执行组件操作
     * 主要流程：
     * 1. 获取材料分组信息
     * 2. 更新分组进度
     * 3. 获取当前分组的材料列表
     * 4. 处理材料分类
     *
     * @return 组件运行结果
     */
    @Override
    protected ComponentRunVO doExecute() {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());

        List<BizInstanceMaterialGroupVO> materialGroup =
                bizInstanceMaterialService.getMaterialGroup(componentDataContext.getInstanceId());

        // 设置材料分组总数
        chatProcessDTO.setMaterialGroupCount(materialGroup.size());
        // 设置当前材料分组步数
        int materialGroupIndex = chatProcessDTO.getMaterialGroupIndex() == null ? 1 : chatProcessDTO.getMaterialGroupIndex();
        chatProcessDTO.setMaterialGroupIndex(materialGroupIndex);

        // 更新缓存
        chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);

        List<BizInstanceMaterialVO> bizInstanceMaterialVOS = materialGroup.get(materialGroupIndex).getInstanceMaterialVOList();

        return processMaterialClassification(bizInstanceMaterialVOS, chatProcessDTO);
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
        // 填充数据
        super.fillData(componentDataContext);
        // 更新当前分组数
        chatProcessDTO.setMaterialGroupIndex(chatProcessDTO.getMaterialGroupIndex() + 1);
        chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);
    }

    @Override
    public String getNextInstruction(ComponentDataContext componentDataContext) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
        if (chatProcessDTO != null
                && chatProcessDTO.getMaterialGroupIndex() != null
                && chatProcessDTO.getMaterialGroupIndex() - 1 >= chatProcessDTO.getMaterialGroupIndex()) {
            return InstructionConstant.RETURN_LAST.getCode();
        }
        return super.getNextInstruction(componentDataContext);
    }
}
