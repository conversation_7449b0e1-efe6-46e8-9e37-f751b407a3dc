package com.workplat.componentEngine.engine;

import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialGroupVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.utils.ChatCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 材料清单分组 组件引擎
 * 渲染逻辑： 1. 获取当前会话的实例ID
 *          2. 取出分组返回
 *
 * <AUTHOR>
 * @date 2025/05/28
 */
@Slf4j
@Service
public class MaterialsListGroupComponentEngine extends AbstractComponentEngine {

    private final BizInstanceMaterialService bizInstanceMaterialService;
    private final BizInstanceInfoService bizInstanceInfoService;
    private final ChatCacheUtil chatCacheUtil;

    public MaterialsListGroupComponentEngine(BizInstanceMaterialService bizInstanceMaterialService,
                                             BizInstanceInfoService bizInstanceInfoService,
                                             ChatCacheUtil chatCacheUtil) {
        this.bizInstanceMaterialService = bizInstanceMaterialService;
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.chatCacheUtil = chatCacheUtil;
    }

    private final String CODE = "MaterialList";

    @Override
    protected ComponentRunVO doExecute() {
        // 获取未初始化且未签收且未上传的文件列表
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());

        List<BizInstanceMaterialGroupVO> materialGroup =
                bizInstanceMaterialService.getMaterialGroup(componentDataContext.getInstanceId());

        // 设置材料分组总数
        chatProcessDTO.setMaterialGroupCount(materialGroup.size());
        // 设置当前材料分组步数
        int materialGroupIndex = chatProcessDTO.getMaterialGroupIndex() == null ? 1 : chatProcessDTO.getMaterialGroupIndex();
        chatProcessDTO.setMaterialGroupIndex(materialGroupIndex);

        List<BizInstanceMaterialVO> bizInstanceMaterialVOS = materialGroup.get(materialGroupIndex).getInstanceMaterialVOList().stream().filter(bizInstanceMaterialVO ->
                !bizInstanceMaterialVO.isBackFill() &&
                        !bizInstanceMaterialVO.isSign()
                        && (bizInstanceMaterialVO.getMaterialFileVOList() == null
                        || bizInstanceMaterialVO.getMaterialFileVOList().isEmpty())
        ).toList();

        // 材料列表组件
        ComponentRunVO.RenderData renderDataMaterialsList = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(bizInstanceMaterialVOS)
                .build();

        // 缓存 添加材料列表
        chatProcessDTO.setMaterialVOS(bizInstanceMaterialVOS);
        chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);

        // 组件运行VO
        ComponentRunVO vo = new ComponentRunVO();
        List<ComponentRunVO.RenderData> renderDataList =
                Collections.singletonList(renderDataMaterialsList);
        vo.setRenderData(renderDataList);
        vo.setTips("材料清单如下，请上传对应材料");
        return vo;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        String instanceId = context.getInstanceId();
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);

        // 判断是否是材料清单列表组件
        return CODE.equals(context.getConfComponent().getEngineCode()) && bizInstanceInfo.getMatterCode().equals("MATERIAL_LIST");
    }
}
