package com.workplat.componentEngine.engine;

import com.alibaba.fastjson2.JSONArray;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.dto.FileClassifyDTO;
import com.workplat.accept.business.chat.service.MaterialClassificationService;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.dto.BizInstanceMaterialSubmitDTO;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.utils.ChatCacheUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;

import java.util.Collection;
import java.util.List;

/**
 * 材料分类组件引擎基类
 * 提供材料分类的通用功能，包括：
 * 1. 材料分类处理
 * 2. 组件处理能力判断
 * 3. 材料提交DTO转换
 *
 * <AUTHOR>
 * @date 2025/06/19
 */
public abstract class AbstractMaterialClassifyEngine extends AbstractComponentEngine {

    /** 材料分类服务 */
    protected final MaterialClassificationService materialClassificationService;
    
    /** 聊天缓存工具 */
    protected final ChatCacheUtil chatCacheUtil;
    
    /** 实例字段服务 */
    protected final BizInstanceFieldsService instanceFieldsService;

    /** 业务实例材料服务 */
    private final BizInstanceMaterialService bizInstanceMaterialService;

    /** 材料分类key */
    @Value("${materialsClassifyKey:}")
    protected String materialsClassifyKey;

    /** 组件代码 */
    protected final String CODE = "MaterialUpload";

    /**
     * 构造函数
     *
     * @param materialClassificationService 材料分类服务
     * @param chatCacheUtil 聊天缓存工具
     * @param instanceFieldsService 实例字段服务
     */
    protected AbstractMaterialClassifyEngine(MaterialClassificationService materialClassificationService,
                                             ChatCacheUtil chatCacheUtil,
                                             BizInstanceFieldsService instanceFieldsService,
                                             BizInstanceMaterialService bizInstanceMaterialService) {
        this.materialClassificationService = materialClassificationService;
        this.chatCacheUtil = chatCacheUtil;
        this.instanceFieldsService = instanceFieldsService;
        this.bizInstanceMaterialService = bizInstanceMaterialService;
    }

    /**
     * 处理材料分类
     * 主要流程：
     * 1. 构建文件分类DTO
     * 2. 获取表单信息
     * 3. 调用材料分类服务处理
     * 4. 更新缓存
     * 5. 组装渲染数据
     *
     * @param bizInstanceMaterialVOS 业务实例材料列表
     * @param chatProcessDTO 聊天处理DTO
     * @return 组件运行结果
     */
    protected ComponentRunVO processMaterialClassification(List<BizInstanceMaterialVO> bizInstanceMaterialVOS,
                                                        ChatProcessDTO chatProcessDTO) {
        // 获取文件分类结果-调用文件分类服务
        FileClassifyDTO dto = FileClassifyDTO.builder()
                .userId(componentDataContext.getInstanceId())
                .fileUrls(chatProcessDTO.getFileUrls())
                .build();

        // 获取表单信息
        BizInstanceFieldsVO bizInstanceFieldsVO =
                instanceFieldsService.queryByInstanceId(componentDataContext.getInstanceId(), true);

        // 处理材料分类
        MaterialClassificationService.MaterialClassificationResult result = 
                materialClassificationService.processClassification(
                        dto,
                        bizInstanceMaterialVOS,
                        bizInstanceFieldsVO,
                        materialsClassifyKey);

        // 存储AI提取的字段到缓存
        chatProcessDTO.setAiExtractFields(result.getExtractedFields());
        chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);

        // 组装渲染数据
        ComponentRunVO vo = new ComponentRunVO();
        ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(result.getMaterialVOS())
                .build();

        vo.setRenderData(List.of(renderData));
        return vo;
    }

    /**
     * 转换业务实例材料提交DTO列表
     * 将业务实例材料VO列表转换为提交DTO列表
     *
     * @param bizInstanceMaterialVOS 业务实例材料VO列表
     * @param instanceId 实例ID
     * @return 业务实例材料提交DTO列表
     */
    protected List<BizInstanceMaterialSubmitDTO> convertInstanceMaterialSubmitDTOS(List<BizInstanceMaterialVO> bizInstanceMaterialVOS, String instanceId) {
        return bizInstanceMaterialVOS.stream()
                .map(bizInstanceMaterialVO -> {
                            BizInstanceMaterialSubmitDTO bizInstanceMaterialSubmitDTO = new BizInstanceMaterialSubmitDTO();
                            BeanUtils.copyProperties(bizInstanceMaterialVO, bizInstanceMaterialSubmitDTO);
                            bizInstanceMaterialSubmitDTO.setInstanceId(instanceId);
                            bizInstanceMaterialSubmitDTO.setMaterialFileList(bizInstanceMaterialVO.getMaterialFileVOList().stream()
                                    .map(bizInstanceMaterialFileVO -> {
                                        BizInstanceMaterialSubmitDTO.MaterialFile materialFile = new BizInstanceMaterialSubmitDTO.MaterialFile();
                                        BeanUtils.copyProperties(bizInstanceMaterialFileVO, materialFile);
                                        return materialFile;
                                    }).toList());
                            return bizInstanceMaterialSubmitDTO;
                        }
                ).filter(bizInstanceMaterialVO -> !bizInstanceMaterialVO.getId().isEmpty()).toList();
    }

    /**
     * 判断是否可以处理给定的组件数据上下文
     *
     * @param context 组件数据上下文
     * @return 如果组件代码匹配则返回true，否则返回false
     */
    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        // 获取提交数据
        Object submitData = componentDataContext.getSubmitData();

        Collection<ComponentRunVO.RenderData> renderDataList =
                JSONArray.parseArray(submitData.toString(), ComponentRunVO.RenderData.class);
        ComponentRunVO.RenderData renderData = renderDataList.iterator().next();

        List<BizInstanceMaterialVO> bizInstanceMaterialVOS = JSONArray.parseArray(renderData.getComponentInfo().toString(), BizInstanceMaterialVO.class);
        // 转换成List<BizInstanceMaterialSubmitDTO>
        List<BizInstanceMaterialSubmitDTO> bizInstanceMaterialSubmitDTOS =
                convertInstanceMaterialSubmitDTOS(bizInstanceMaterialVOS, componentDataContext.getInstanceId());
        // 调用批量保存接口
        bizInstanceMaterialService.tempSubmit(bizInstanceMaterialSubmitDTOS);
    }
}