package com.workplat.componentEngine.engine;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.api.api.BizInstanceSituationApi;
import com.workplat.gss.application.api.dto.BizInstanceQuotaDto;
import com.workplat.gss.application.dubbo.vo.ConfMatterAcceptQuotaVO;
import com.workplat.gss.common.core.response.ResponseData;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 情境组件引擎
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@Service
public class SituationComponentEngine extends AbstractComponentEngine {

    private final BizInstanceSituationApi bizInstanceSituationApi;

    // 定义该引擎支持的组件编码
    private static final String CODE = "ScenarioSelection";

    SituationComponentEngine(BizInstanceSituationApi bizInstanceSituationApi) {
        this.bizInstanceSituationApi = bizInstanceSituationApi;
    }

    @Override
    protected ComponentRunVO doExecute() {
        //  调用API获取数据
        ResponseData<List<ConfMatterAcceptQuotaVO>> listResponseData =
                bizInstanceSituationApi.get(componentDataContext.getInstanceId());
        bizInstanceSituationApi.get(componentDataContext.getInstanceId());
        List<ConfMatterAcceptQuotaVO> data = listResponseData.getData();

        // 封装返回数据
        ComponentRunVO componentRunVO = new ComponentRunVO();
        ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(data)
                .build();
        //  设置返回数据
        componentRunVO.setRenderData(List.of(renderData));
        return componentRunVO;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        // 获取提交数据
        Object submitData = componentDataContext.getSubmitData();

        Collection<ComponentRunVO.RenderData> renderDataList =
                JSONArray.parseArray(submitData.toString(), ComponentRunVO.RenderData.class);
        ComponentRunVO.RenderData renderData = renderDataList.iterator().next();

        List<BizInstanceQuotaDto> bizInstanceQuotas =
                JSONArray.parseArray(renderData.getComponentInfo().toString(), BizInstanceQuotaDto.class);

        bizInstanceQuotas.forEach(bizInstanceQuotaDto -> {
            // 添加其他字段赋值逻辑
            bizInstanceQuotaDto.setInstanceId(componentDataContext.getInstanceId());

            if (bizInstanceQuotaDto.getOptions() != null && !bizInstanceQuotaDto.getOptions().isEmpty()) {
                // 处理子选项（options）
                bizInstanceQuotaDto.getOptions().forEach(option -> {
                    // 如果 options 里面还有 quotas，继续处理嵌套结构
                    if (option.getQuotas() != null && !option.getQuotas().isEmpty()) {
                        option.getQuotas().forEach(nestedQuota -> {
                            nestedQuota.setInstanceId(componentDataContext.getInstanceId());
                            // 可选：添加其他字段赋值逻辑
                        });
                    }
                });
            }
        });
        // 保存数据
        bizInstanceSituationApi.save(bizInstanceQuotas);
    }
}