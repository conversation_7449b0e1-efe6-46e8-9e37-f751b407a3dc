package com.workplat.componentEngine.engine;

import cn.hutool.core.map.MapUtil;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.utils.ChatCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 材料清单 组件引擎
 * 渲染逻辑： 1. 获取当前会话的实例ID，并查询出未初始化且未签收且未上传的文件列表，并渲染为材料清单组件。
 *
 * <AUTHOR>
 * @date 2025/05/28
 */
@Slf4j
@Service
public class MaterialsListComponentEngine extends AbstractComponentEngine {

    private final BizInstanceMaterialService bizInstanceMaterialService;
    private final ChatCacheUtil chatCacheUtil;

    public MaterialsListComponentEngine(BizInstanceMaterialService bizInstanceMaterialService, ChatCacheUtil chatCacheUtil) {
        this.bizInstanceMaterialService = bizInstanceMaterialService;
        this.chatCacheUtil = chatCacheUtil;
    }

    private final String CODE = "MaterialList";

    @Override
    protected ComponentRunVO doExecute() {
        // 获取未初始化且未签收且未上传的文件列表
        List<BizInstanceMaterialVO> instanceMaterialNonInit =
                bizInstanceMaterialService.getInstanceMaterialNonInit(componentDataContext.getInstanceId());
        List<BizInstanceMaterialVO> bizInstanceMaterialVOS = instanceMaterialNonInit.stream().filter(bizInstanceMaterialVO ->
                !bizInstanceMaterialVO.isBackFill() &&
                        !bizInstanceMaterialVO.isSign()
                && (bizInstanceMaterialVO.getMaterialFileVOList() == null
                        || bizInstanceMaterialVO.getMaterialFileVOList().isEmpty())
        ).toList();
        //
        //// 示例组件TestA
        //ComponentRunVO.RenderData renderDataTestA = ComponentRunVO.RenderData.builder()
        //        .componentName("TestA")
        //        .componentInfo(MapUtil.builder().put("title", "我是组件A的标题").build())
        //        .build();
        //// 示例组件TestB
        //ComponentRunVO.RenderData renderDataTestB = ComponentRunVO.RenderData.builder()
        //        .componentName("TestB")
        //        .componentInfo(MapUtil.builder().put("title", "我是组件B的标题").build())
        //        .build();
        // 材料列表组件
        ComponentRunVO.RenderData renderDataMaterialsList = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(bizInstanceMaterialVOS)
                .build();

        // 缓存 添加材料列表
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
        chatProcessDTO.setMaterialVOS(bizInstanceMaterialVOS);
        chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);

        // 组件运行VO
        ComponentRunVO vo = new ComponentRunVO();
        List<ComponentRunVO.RenderData> renderDataList =
                Collections.singletonList(renderDataMaterialsList);
        vo.setRenderData(renderDataList);
        vo.setTips("材料清单如下，请上传对应材料");
        return vo;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        // 判断是否是材料清单列表组件
        return CODE.equals(context.getConfComponent().getEngineCode());
    }
}
