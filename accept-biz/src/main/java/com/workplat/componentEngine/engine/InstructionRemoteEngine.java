package com.workplat.componentEngine.engine;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.vo.BizInstanceReadNoticeVO;
import com.workplat.gss.service.item.dubbo.matter.constant.MatterPublicType;
import com.workplat.gss.service.item.dubbo.matter.service.ConfMatterPublicService;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> cheng
 * @package com.workplat.componentEngine.engine
 * @description 申报须知（第三方接口获取）组件引擎
 * @date 2025/5/28 14:41
 */
@Service
public class InstructionRemoteEngine extends AbstractComponentEngine {

    private final String CODE = "InstructionRemote";
    private final RestTemplate restTemplate;
    private final BizInstanceInfoService bizInstanceInfoService;
    private final ConfMatterPublicService confMatterPublicService;

    public InstructionRemoteEngine(RestTemplate restTemplate,
                                   BizInstanceInfoService bizInstanceInfoService,
                                   ConfMatterPublicService confMatterPublicService) {
        this.restTemplate = restTemplate;
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.confMatterPublicService = confMatterPublicService;
    }

    @Override
    protected ComponentRunVO doExecute() {
        // TODO 后续完善 接口调取来源
        // 获取当前办件信息
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(componentDataContext.getInstanceId());
        BizInstanceReadNoticeVO bizInstanceReadNoticeVO;
        try {
            String basicJson = confMatterPublicService.getAnyJson(bizInstanceInfo.getMatterPublicId(), MatterPublicType.BASIC_JSON);
            bizInstanceReadNoticeVO = JSONObject.parseObject(basicJson, BizInstanceReadNoticeVO.class);
        } catch (Exception e) {
            throw new RuntimeException("初始化申报阅读须知失败：", e);
        }

        String url = "http://192.168.124.247:8085/blbb/app/ai/blbb/getFirstHandHomeInfoByCertNo";

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + "test");
        ResponseEntity<String> exchange = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(headers), String.class);
        String str = exchange.getBody();
        if (null != str) {

            List<List<?>> lists = JSON.parseObject(str, new TypeReference<>() {
            });
            List<InstructionRemoteVO> instructionRemoteVOS = new ArrayList<>();
            for (List<?> list : lists) {
                List<FieldItem> fieldItems = new ArrayList<>();
                for (Object o : list) {
                    FieldItem fieldItem = JSONObject.parseObject(o.toString(), FieldItem.class);
                    fieldItems.add(fieldItem);
                }
                InstructionRemoteVO vo = new InstructionRemoteVO();
                vo.setSelected(false);
                vo.setList(fieldItems);
                instructionRemoteVOS.add(vo);
            }

            InstructionRemoteData instructionRemoteData = new InstructionRemoteData();
            instructionRemoteData.setInstructionRemoteVOS(instructionRemoteVOS);

            ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                    .componentName(CODE)
                    .componentInfo(instructionRemoteData)
                    .build();

            ComponentRunVO vo = new ComponentRunVO();
            vo.setRenderData(List.of(renderData));

            vo.setTips("请选择此次办理的合同");
            return vo;
        }

        // 如果接口返回结果为空，则返回配置的文本进行提示
        InstructionRemoteData instructionRemoteData = new InstructionRemoteData();
        instructionRemoteData.setNoDataTips("您好！经我们的系统查询，目前尚未发现您名\n" +
                "    下存在已备案的一手房合同记录。为确保您的\n" +
                "    权益，请首先确认是否已经向太仓市住房和城\n" +
                "    乡建设局提交了相关合同备案手续。如果还未\n" +
                "    进行备案，请您尽快完成此步骤后再继续办理\n" +
                "    一手房转移登记业务，假若您先前已经完成了\n" +
                "    合同备案流程，我们建议您再次核对备案时提\n" +
                "    供的身份信息是否有误。欢迎您直接联系相关\n" +
                "    部门进行详细咨询，以协助解决可能出现的任\n" +
                "    何凝问或问题。");

        ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(instructionRemoteData)
                .build();
        ComponentRunVO vo = new ComponentRunVO();
        vo.setRenderData(List.of(renderData));

        vo.setTips("无合同");
        return vo;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Getter
    @Setter
    static class InstructionRemoteData {
        // 没数据提示内容
        private String noDataTips;
        private List<InstructionRemoteVO> instructionRemoteVOS;
    }

    @Getter
    @Setter
    static class InstructionRemoteVO {
        private boolean selected;
        private List<FieldItem> list;
    }

    @Getter
    @Setter
    static class FieldItem {
        String name;
        String code;
        String value;
    }

    // 无合同的返回
    @Getter
    @Setter
    static class NoData {
        // 内容
        private String content;
        // 是否可以回退
        private boolean canBack;
    }
}
