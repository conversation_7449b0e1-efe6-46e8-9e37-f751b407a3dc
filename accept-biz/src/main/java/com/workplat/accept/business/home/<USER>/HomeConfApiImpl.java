package com.workplat.accept.business.home.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import com.workplat.accept.business.home.dto.ConfMatterCatalogDto;
import com.workplat.accept.business.home.dto.HomeConfDto;
import com.workplat.accept.business.home.entity.HomeConf;
import com.workplat.accept.business.home.service.HomeConfService;
import com.workplat.accept.business.home.vo.ConfMatterCatalogVO;
import com.workplat.accept.business.home.vo.HomeConfVO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.service.item.dubbo.matter.entity.ConfMatter;
import com.workplat.gss.service.item.dubbo.matter.service.ConfMatterCatalogService;
import com.workplat.gss.service.item.dubbo.matter.service.ConfMatterService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * @Author: Odin
 * @Date: 2024/9/24 16:43
 * @Description:
 */

@RestController
public class HomeConfApiImpl implements HomeConfApi {

    @Resource
    private HomeConfService homeConfService;

    @Resource
    ConfMatterService confMatterService;

    @Override
    public ResponseData<LinkedHashMap<String, List<HomeConfVO>>> getHomeConfList() {
        return ResponseData.success(homeConfService.getHomeConfList());
    }

    @Override
    public ResponseData<Void> save(@Valid HomeConfDto homeConfDto) {
        homeConfService.saveHomeConf(homeConfDto);
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<HomeConfVO> getByRelatedConfMatterId(String confMatterId) {
        return ResponseData.success(homeConfService.queryByConfMatterId(confMatterId));
    }

    @Override
    public ResponseData<Void> deleteMatter(String id) {
        homeConfService.deleteMatter(id);
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<ConfMatterCatalogVO> save(ConfMatterCatalogDto confMatterCatalogDto) {
        return ResponseData.success(homeConfService.saveMatter(confMatterCatalogDto));
    }

}
