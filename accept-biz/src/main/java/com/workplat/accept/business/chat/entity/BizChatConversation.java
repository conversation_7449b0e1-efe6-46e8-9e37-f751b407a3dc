package com.workplat.accept.business.chat.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Comment;

@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "biz_chat_conversation")
@Getter
@Setter
public class BizChatConversation extends BaseEntity {

    @Comment("用户ID")
    @Column(name = "user_id", length = 64)
    private String userId;

    @Comment("会话标题")
    @Column(name = "title", length = 255)
    private String title;

    @Comment("渠道")
    @Column(name = "channel", length = 32)
    private String channel;
} 