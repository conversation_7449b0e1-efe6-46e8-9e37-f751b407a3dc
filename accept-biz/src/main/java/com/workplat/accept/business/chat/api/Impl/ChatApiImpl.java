package com.workplat.accept.business.chat.api.Impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.workplat.accept.business.chat.api.ChatApi;
import com.workplat.accept.business.chat.dto.*;
import com.workplat.accept.business.chat.entity.BizChatConversation;
import com.workplat.accept.business.chat.service.*;
import com.workplat.accept.business.chat.service.Impl.ChatComponentService;
import com.workplat.accept.business.chat.service.Impl.ChatConversationService;
import com.workplat.accept.business.chat.service.Impl.ChatMessageService;
import com.workplat.accept.business.chat.service.Impl.ChatStreamService;
import com.workplat.accept.business.chat.vo.*;
import com.workplat.componentEngine.dto.ComponentRunDTO;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.vo.BizInstanceInfoVO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.utils.ChatCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/api/chat")
public class ChatApiImpl implements ChatApi {

    private final ChatComponentService chatComponentService;
    private final ChatConversationService chatConversationService;
    private final ChatMessageService chatMessageService;
    private final ChatStreamService chatStreamService;
    private final DifyServiceHelper difyServiceHelper;
    private final ChatCacheUtil chatCacheUtil;
    private final BizInstanceInfoService bizInstanceInfoService;
    private final MattersClassifyService mattersClassifyService;

    @Value("${entranceAgentKey:app-bgqibOyJsvdDBfbbXTpp346p}")
    private String DEFAULT_APP_KEY;

    // 字段提取key
    @Value("${FieldExtractionKey:app-bGVVIyj8ZoTiSuLjVnp9wYxJ}")
    private String FieldExtractionKey = "fileWithdrawKey";

    // 组件运行结束标识 给智能体
    private final String COMPONENT_RUN_END = "suCceSs_coMpoNent_eNd";

    public ChatApiImpl(ChatComponentService chatComponentService,
                       ChatConversationService chatConversationService,
                       ChatMessageService chatMessageService,
                       ChatStreamService chatStreamService,
                       DifyServiceHelper difyServiceHelper,
                       ChatCacheUtil chatCacheUtil,
                       BizInstanceInfoService bizInstanceInfoService,
                       MattersClassifyService mattersClassifyService) {
        this.chatComponentService = chatComponentService;
        this.chatConversationService = chatConversationService;
        this.chatMessageService = chatMessageService;
        this.chatStreamService = chatStreamService;
        this.difyServiceHelper = difyServiceHelper;
        this.chatCacheUtil = chatCacheUtil;
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.mattersClassifyService = mattersClassifyService;
    }

    @Override
    public ResponseData<ComponentRunVO> componentRun(ComponentRunDTO componentRunDto) {
        return chatComponentService.componentRun(componentRunDto);
    }

    @Override
    public Flux<ServerSentEvent<String>> ask(AskDTO ask) throws JsonProcessingException {
        log.debug("Asking question: {}", ask.getQuestions());

        if (ask.getComponentRunDto() == null) {
            return Flux.error(new IllegalArgumentException("ComponentRunDto is null"));
        }

        ChatProcessDTO chatProcessDTO;
        String recordId = ask.getRecordId();

        if (recordId == null || recordId.isEmpty()) {
            BizChatConversation conversation = chatConversationService.createConversation(ask.getUserId(), ask.getQuestions(), ask.getChannel());
            recordId = conversation.getId();
            ask.setRecordId(recordId);
            chatProcessDTO = new ChatProcessDTO();
            chatProcessDTO.setRecordId(recordId);
        } else {
            chatMessageService.saveUserMessage(recordId, ask.getQuestions());
            chatProcessDTO = chatCacheUtil.get(recordId);

            if (chatProcessDTO != null && StringUtils.isNotBlank(chatProcessDTO.getAgentKey())) {
                ask.setAppKey(chatProcessDTO.getAgentKey());
            }
        }

        String appKey = StringUtils.isNotBlank(ask.getAppKey()) ? ask.getAppKey() : DEFAULT_APP_KEY;

        // 处理特定的材料上传
        if (ask.getFileUrls() != null && !ask.getFileUrls().isEmpty()
                && "MaterialList".equals(ask.getComponentRunDto().getEngineCode())) {
            chatMessageService.saveUserMessage(recordId, JSONObject.toJSONString(ask.getFileUrls()));
            ask.getComponentRunDto().setSubmitData(JSONObject.toJSONString(ask.getFileUrls()));
            if (chatProcessDTO != null) {
                chatProcessDTO.setFileUrls(ask.getFileUrls());
            }
        }

        // 缓存记录
        chatCacheUtil.set(recordId, chatProcessDTO);

        // 更新组件运行数据
        if (ask.getComponentRunDto().getSubmitData() != null
                && !"{}".equals(ask.getComponentRunDto().getSubmitData().toString())) {
            // 处理组件运行数据
            chatComponentService.BackFillData(ask.getComponentRunDto());
            // 更新组件运行数据
            chatMessageService.UpdateComponentRun(ask.getComponentRunDto());
            // 获取下一步指令
            ask.setQuestions((String) chatComponentService.getNextInstruction(ask.getComponentRunDto()));
        }

        Flux<StreamMessageVO> streamMessageVOFlux = difyServiceHelper.streamingMessage(ask, appKey);
        return chatStreamService.processStreamMessages(ask, streamMessageVOFlux);
    }

    @Override
    public ResponseData<Object> askBlock(AskDTO ask) {
        // 初始化
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(ask.getRecordId());
        init(ask, chatProcessDTO);
        chatCacheUtil.set(ask.getRecordId(), chatProcessDTO);
        // 获取阻塞消息
        BlockMessageVO blockMessageVO = difyServiceHelper.blockingMessage(ask, ask.getAppKey());
        blockMessageVO.setRecordId(ask.getRecordId());
        return ResponseData.success(blockMessageVO);
    }

    private void init(AskDTO ask, ChatProcessDTO chatProcessDTO) {
        if (ask.getQuestions().startsWith("定义业务办理流程")) {
            // 创建 ObjectMapper 实例
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                // 从字符串中提取 JSON 部分
                int jsonStartIndex = ask.getQuestions().indexOf('{');
                String jsonString = ask.getQuestions().substring(jsonStartIndex);

                // 解析 JSON 字符串为 JsonNode 对象
                JsonNode jsonNode = objectMapper.readTree(jsonString);

                // 获取 matterId 的值
                String matterId = jsonNode.get("matterId").asText();
                String flowCode = jsonNode.get("flowCode").asText();

                // 假设给了我们一个数据 {"matterId":"190803ae4c734010af8b757a72ec3e62"}
                BizInstanceInfoVO bizInstanceInfoVO = bizInstanceInfoService.initialize(matterId);
                // 更新进入缓存
                chatProcessDTO.setInstanceId(bizInstanceInfoVO.getId());
                chatProcessDTO.setFlowCode(flowCode);
            } catch (Exception e) {
                throw new RuntimeException("初始化办件失败", e);
            }
        }
    }

    @Override
    public ResponseData<Page<BizChatConversationVO>> getConversationPage(BizChatConversationDTO dto, PageableDTO pageable) {
        return ResponseData.success(chatConversationService.getConversationPage(dto, pageable));
    }

    @Override
    public ResponseData<BizChatConversationVO> getLatestConversation(BizChatConversationDTO dto) {
        try {
            return ResponseData.success(chatConversationService.getLatestConversation(dto));
        } catch (Exception e) {
            log.error("getLatestConversation error", e);
        }
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<Void> deleteConversation(DeleteConversationDTO deleteConversationDTO) {
        chatConversationService.deleteConversation(deleteConversationDTO);
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<List<BizChatMessageVO>> getConversationMessages(BizChatMessageDTO dto) {
        return chatMessageService.getConversationMessages(dto);
    }

    @Override
    public ResponseData<Void> updateFileUploadList(UpdateFileUploadDTO updateFileUploadDTO) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(updateFileUploadDTO.getRecordId());
        chatProcessDTO.setMaterialSubmitVOS(updateFileUploadDTO.getMaterialSubmitVOS());
        chatCacheUtil.set(updateFileUploadDTO.getRecordId(), chatProcessDTO);
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<Void> stopChat(StopChatDTO stopChatDTO) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(stopChatDTO.getRecordId());
        difyServiceHelper.stopChatMessageTask(stopChatDTO.getTaskId(), stopChatDTO.getUserId(),
                Objects.nonNull(chatProcessDTO.getAgentKey()) ? chatProcessDTO.getAgentKey() : DEFAULT_APP_KEY);
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<Void> recordChat(BizChatMessageDTO dto) {
        return chatMessageService.recordChat(dto);
    }

    @Override
    public ResponseData<Object> withdrawBl(FileClassifyDTO dto) {
        JSONArray jsonArray = mattersClassifyService.withdrawBl(dto, FieldExtractionKey);
        return ResponseData.success().data(jsonArray.getFirst());
    }

    @Override
    public ResponseData<Void> commitSign(CommitSignDTO dto) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(dto.getRecordId());
        chatProcessDTO.setSignFileId(dto.getSignFileId());
        chatCacheUtil.set(dto.getRecordId(), chatProcessDTO);
        // 提交签名
        FileDTO fileDTO = new FileDTO();
        fileDTO.setId(dto.getSignFileId());
        //bizInstanceMaterialCustomizeService.sign(dto.getInstanceId(), Collections.singletonList(fileDTO));
        return ResponseData.success().build();
    }
}