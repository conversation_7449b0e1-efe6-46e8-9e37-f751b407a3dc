package com.workplat.accept.user.util;

import cn.hutool.http.HttpRequest;
import com.workplat.gateway.client.GatewayClientRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.HttpURLConnection;
import java.util.Map;



/**
 * @Author: Odin
 * @Date: 2024/9/27 19:07
 * @Description:
 */

@Slf4j
@Component
public class GatewayUtils {

    private static String allinoneUrlPrefix = "https://zwfw.taicang.gov.cn/gateway-api/allinone-api";

    private static final GatewayClientRequest gatewayClientRequest = GatewayClientRequest.builder(

            "https://zwfw.taicang.gov.cn/gateway-api",

            "53C23A228B4C4FFB81A70E1D5C3C8283",

            "04f0aada158a4ed086a2df96787dff4d525166d566886f8b2b8b80973236bd85b9c8e585dcf8e9d461390afcf7957ca5b755c8b6e3809a7f686f98975bb990a982",

            "",

            ""

    );

    public static String getAllinoneApiResult(String url, Map<String, Object> formMap) {
        log.info("请求allinone接口：{}", allinoneUrlPrefix + url);
        String res = gatewayClientRequest.execute(HttpRequest.post(allinoneUrlPrefix + url).form(formMap), false, false);
        log.info("allinone接口 - {} -返回结果：{}", allinoneUrlPrefix + url, res);
        return res;
    }

}
